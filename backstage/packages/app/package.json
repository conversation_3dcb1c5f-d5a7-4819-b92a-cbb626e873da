{"name": "app", "version": "0.0.0", "private": true, "bundled": true, "backstage": {"role": "frontend"}, "scripts": {"start": "backstage-cli package start", "build": "backstage-cli package build", "clean": "backstage-cli package clean", "test": "backstage-cli package test", "lint": "backstage-cli package lint"}, "dependencies": {"@backstage/app-defaults": "^1.6.4", "@backstage/catalog-model": "^1.7.5", "@backstage/cli": "^0.33.1", "@backstage/core-app-api": "^1.18.0", "@backstage/core-components": "^0.17.4", "@backstage/core-plugin-api": "^1.10.9", "@backstage/integration-react": "^1.2.9", "@backstage/plugin-api-docs": "^0.12.9", "@backstage/plugin-catalog": "^1.31.1", "@backstage/plugin-catalog-common": "^1.1.5", "@backstage/plugin-catalog-graph": "^0.4.21", "@backstage/plugin-catalog-import": "^0.13.3", "@backstage/plugin-catalog-react": "^1.19.1", "@backstage/plugin-home": "^0.8.10", "@backstage/plugin-kubernetes": "^0.12.9", "@backstage/plugin-org": "^0.6.41", "@backstage/plugin-permission-react": "^0.4.36", "@backstage/plugin-scaffolder": "^1.33.0", "@backstage/plugin-search": "^1.4.28", "@backstage/plugin-search-react": "^1.9.2", "@backstage/plugin-techdocs": "^1.13.2", "@backstage/plugin-techdocs-module-addons-contrib": "^1.1.26", "@backstage/plugin-techdocs-react": "^1.3.1", "@backstage/plugin-user-settings": "^0.8.24", "@backstage/theme": "^0.6.7", "@backstage/ui": "^0.6.0", "@internal/plugin-choreo-integration": "workspace:^", "@material-ui/core": "^4.12.2", "@material-ui/icons": "^4.9.1", "react": "^18.0.2", "react-dom": "^18.0.2", "react-router": "^6.3.0", "react-router-dom": "^6.3.0"}, "devDependencies": {"@backstage/test-utils": "^1.7.10", "@playwright/test": "^1.32.3", "@testing-library/dom": "^9.0.0", "@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.0.0", "@types/react-dom": "*", "cross-env": "^7.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "files": ["dist"]}