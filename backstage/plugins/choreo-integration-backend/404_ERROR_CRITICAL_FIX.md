# 404 Error Critical Fix - Service Creation

## Root Cause Identified

The 404 Not Found error was caused by using the **wrong API endpoint URL** for the GraphQL request in the service creation implementation.

## The Problem

**Service Creation (INCORRECT):**
```typescript
const response = await fetch(`${apiUrl}/graphql`, {
  // ... request details
});
```

**Web App Creation (CORRECT):**
```typescript
const response = await fetch(`${projectApiUrl}/graphql`, {
  // ... request details
});
```

## The Fix

### 1. Corrected API Endpoint

**Before:**
```typescript
const response = await fetch(`${apiUrl}/graphql`, {
```

**After:**
```typescript
const response = await fetch(`${projectApiUrl}/graphql`, {
```

**Rationale:** The GraphQL mutations for component creation must use the `projectApiUrl` endpoint, not the general `apiUrl`. This is consistent with how the working web app creation is implemented.

### 2. Enhanced Error Handling

**Added Specific Error Handling:**
```typescript
if (!response.ok) {
  logger.error('Choreo API HTTP error', {
    status: response.status,
    statusText: response.statusText,
  });
  if (response.status === 404) {
    throw new NotFoundError('Choreo GraphQL API endpoint not found');
  }
  if (response.status >= 500) {
    throw new ServiceUnavailableError(
      `Choreo API service unavailable: ${response.statusText}`,
    );
  }
  throw new Error(`HTTP error! status: ${response.status}`);
}
```

**Rationale:** Provides better error classification and debugging information, matching the pattern used in web app creation.

### 3. Fixed Port Default Value

**Before:**
```typescript
port: data.buildConfig.port || servicePreset?.defaultConfig.port || null,
```

**After:**
```typescript
port: data.buildConfig.port || servicePreset?.defaultConfig.port || 3000,
```

**Rationale:** Ensures a valid numeric port value instead of null, which could cause validation issues.

## Configuration Context

The fix relies on the correct configuration in `app-config.yaml`:

```yaml
choreo:
  apiUrl: 'https://apis.choreo.dev/apis/1.0.0'          # General API endpoint
  projectApiUrl: 'https://apis.choreo.dev/projects/1.0.0' # Project/GraphQL endpoint
  bearerToken: '${CHOREO_BEARER_TOKEN}'
```

## Why This Fixes the 404 Error

1. **Correct Endpoint**: The `projectApiUrl` endpoint (`https://apis.choreo.dev/projects/1.0.0/graphql`) is where the GraphQL mutations are hosted
2. **Consistent Pattern**: Matches the working web app creation implementation exactly
3. **Proper Authentication**: Uses the same authentication headers and bearer token

## Files Modified

- `backstage/plugins/choreo-integration-backend/src/services/ChoreoApiService/createChoreoApiService.ts`

## Expected Outcome

After this fix:

1. ✅ **No More 404 Errors**: The GraphQL request will reach the correct endpoint
2. ✅ **Successful Component Creation**: Service components should be created successfully
3. ✅ **Proper Error Handling**: Better error messages for debugging
4. ✅ **Consistent Behavior**: Service creation follows the same pattern as web app creation

## Testing

To verify the fix:

1. Navigate to the service creation page
2. Fill out the form with valid repository details
3. Select Node.js build preset
4. Submit the form
5. Verify successful component creation without 404 errors

The backend logs should now show successful GraphQL responses instead of 404 Not Found errors.

## Critical Insight

**The key lesson**: Component creation mutations (both web apps and services) must use the `projectApiUrl` endpoint, not the general `apiUrl`. This is a critical distinction in the Choreo API architecture where:

- `apiUrl` is for general operations (organizations, projects, components listing)
- `projectApiUrl` is for GraphQL mutations (component creation, updates, etc.)

This fix resolves the fundamental routing issue that was preventing service creation from working.
