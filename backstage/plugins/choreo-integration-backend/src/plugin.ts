import {
  coreServices,
  createBackendPlugin,
} from '@backstage/backend-plugin-api';
import { createRouter } from './router';
import { createChoreoApiService } from './services/ChoreoApiService';

/**
 * choreoIntegrationPlugin backend plugin
 *
 * @public
 */
export const choreoIntegrationPlugin = createBackendPlugin({
  pluginId: 'choreo-integration',
  register(env) {
    env.registerInit({
      deps: {
        logger: coreServices.logger,
        httpAuth: coreServices.httpAuth,
        httpRouter: coreServices.httpRouter,
        config: coreServices.rootConfig,
      },
      async init({ logger, httpAuth, httpRouter, config }) {
        const choreoApiService = await createChoreoApiService({
          logger,
          config,
        });

        httpRouter.use(
          await createRouter({
            httpAuth,
            choreoApiService,
          }),
        );
      },
    });
  },
});
