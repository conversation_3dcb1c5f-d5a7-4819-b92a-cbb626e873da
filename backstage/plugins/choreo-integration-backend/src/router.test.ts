import {
  mockErrorHandler,
  mockServices,
} from '@backstage/backend-test-utils';
import express from 'express';
import request from 'supertest';

import { createRouter } from './router';
import { ChoreoApiService } from './services/ChoreoApiService/types';

// TEMPLATE NOTE:
// Testing the router directly allows you to write a unit test that mocks the provided options.
describe('createRouter', () => {
  let app: express.Express;
  let choreoApiService: jest.Mocked<ChoreoApiService>;

  beforeEach(async () => {
    choreoApiService = {
      getOrganizations: jest.fn(),
    };
    const router = await createRouter({
      httpAuth: mockServices.httpAuth(),
      choreoApiService,
    });
    app = express();
    app.use(router);
    app.use(mockErrorHandler());
  });

  it('should fetch Choreo organizations', async () => {
    const mockOrgs = [
      {
        id: '74908',
        uuid: '1056246d-5204-405f-945d-12791bdfe2ef',
        handle: 'pasindui',
        name: 'pasind<PERSON>',
        owner: {
          id: '60929',
          idpId: '3622cc93-36ad-4272-80dc-a7c19649f618',
          createdAt: '2025-07-09T09:50:18.947Z',
        },
      },
    ];

    choreoApiService.getOrganizations.mockResolvedValue(mockOrgs);

    const response = await request(app).get('/choreo-orgs');

    expect(response.status).toBe(200);
    expect(response.body).toEqual({
      organizations: mockOrgs,
      status: 'success',
      count: 1,
    });
  });

  it('should handle errors when fetching Choreo organizations', async () => {
    choreoApiService.getOrganizations.mockRejectedValue(
      new Error('API Error'),
    );

    const response = await request(app).get('/choreo-orgs');

    expect(response.status).toBe(500);
    expect(response.body).toEqual({
      error: 'Failed to fetch Choreo organizations',
      status: 'error',
      message: 'API Error',
    });
  });
});
