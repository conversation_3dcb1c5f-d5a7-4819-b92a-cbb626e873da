import {
  startTestBackend,
  mockServices,
} from '@backstage/backend-test-utils';
import { choreoIntegrationPlugin } from './plugin';
import request from 'supertest';

// TEMPLATE NOTE:
// Plugin tests are integration tests for your plugin, ensuring that all pieces
// work together end-to-end. You can still mock injected backend services
// however, just like anyone who installs your plugin might replace the
// services with their own implementations.
describe('plugin', () => {
  it('should handle choreo-orgs endpoint with missing config', async () => {
    const { server } = await startTestBackend({
      features: [
        choreoIntegrationPlugin,
        mockServices.rootConfig.factory({
          data: {
            // Missing choreo config to test error handling
          },
        }),
      ],
    });

    const response = await request(server).get('/api/choreo-integration/choreo-orgs');

    expect(response.status).toBe(500);
    expect(response.body).toEqual({
      error: 'Failed to fetch Choreo organizations',
      status: 'error',
      message: expect.stringContaining('configuration is missing'),
    });
  });

  it('should handle choreo-orgs endpoint with valid config', async () => {
    const { server } = await startTestBackend({
      features: [
        choreoIntegrationPlugin,
        mockServices.rootConfig.factory({
          data: {
            choreo: {
              apiUrl: 'https://apis.choreo.dev/orgs/1.0.0',
              projectApiUrl: 'https://apis.choreo.dev/projects/1.0.0',
              bearerToken: 'test-token',
            },
          },
        }),
      ],
    });

    // This will fail because we don't have a real API, but it should at least
    // get past the configuration validation
    const response = await request(server).get('/api/choreo-integration/choreo-orgs');

    expect(response.status).toBe(500);
    expect(response.body).toHaveProperty('error');
    expect(response.body).toHaveProperty('status', 'error');
  });
});
