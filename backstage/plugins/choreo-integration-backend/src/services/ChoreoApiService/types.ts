import { BackstageCredentials } from '@backstage/backend-plugin-api';

export interface ChoreoOrg {
  id: string;
  uuid: string;
  handle: string;
  name: string;
  owner: {
    id: string;
    idpId: string;
    createdAt: string;
  };
}

export interface ChoreoProject {
  id: string;
  orgId: string;
  name: string;
  version: string;
  createdDate: string;
  handler: string;
  region: string;
  description?: string;
  defaultDeploymentPipelineId?: string;
  deploymentPipelineIds?: string[];
  type?: string;
  gitProvider?: string;
  gitOrganization?: string;
  repository?: string;
  branch?: string;
  secretRef?: string;
  updatedAt: string;
}

export interface ProjectCreationEligibility {
  isProjectCreationAllowed: boolean;
}

// Component-related interfaces
export interface ChoreoComponentRepository {
  buildpackConfig?: {
    versionId: string;
    buildContext: string;
    languageVersion: string;
    buildpack: {
      id: string;
      language: string;
    };
  };
  byocWebAppBuildConfig?: {
    id: string;
    dockerContext: string;
    webAppType: string;
  };
}

export interface ChoreoComponentApiVersion {
  apiVersion: string;
  proxyName: string;
  proxyUrl: string;
  proxyId: string;
  id: string;
  state: string;
  latest: boolean;
  branch: string;
  accessibility: string;
}

export interface ChoreoComponentDeploymentTrack {
  id: string;
  createdAt: string;
  updatedAt: string;
  apiVersion: string;
  branch: string;
  description: string;
  componentId: string;
  latest: boolean;
  versionStrategy: string;
  autoDeployEnabled: boolean;
}

export interface ChoreoComponent {
  projectId: string;
  id: string;
  description?: string;
  status: string;
  initStatus: string;
  name: string;
  handler: string;
  displayName: string;
  displayType: string;
  version: string;
  createdAt: string;
  lastBuildDate?: string;
  orgHandler: string;
  isSystemComponent: boolean;
  repository?: ChoreoComponentRepository;
  componentSubType: string;
  apiVersions?: ChoreoComponentApiVersion[];
  deploymentTracks?: ChoreoComponentDeploymentTrack[];
}

// Environment-related interfaces
export interface ChoreoEnvironment {
  name: string;
  id: string;
  choreoEnv: string;
  vhost: string;
  apiEnvName: string;
  isMigrating: boolean;
  apimEnvId: string;
  namespace: string;
  sandboxVhost: string;
  critical: boolean;
  isPdp: boolean;
  promoteFrom?: string;
  dpId: string;
  templateId: string;
  scaleToZeroEnabled: boolean;
}

// Web App Component Creation interfaces
export interface WebAppBuildConfig {
  buildCommand?: string;
  buildPath?: string;
  nodeVersion?: string;
  languageVersion?: string;
  port?: number;
  runCommand?: string;
  dockerfilePath?: string;
}

export interface WebAppCreationData {
  repositoryUrl: string;
  branch: string;
  componentDirectory?: string;
  displayName: string;
  componentName: string;
  description?: string;
  buildPreset: string;
  buildConfig: WebAppBuildConfig;
  orgId: number; // Added orgId
  orgHandler: string;
  projectId: string;
}

// Repository Validation interfaces
export interface RepositoryValidationOptions {
  credentials: BackstageCredentials;
  organizationName: string;
  repoName: string;
  branch: string;
  subPath?: string;
  dockerFilePath?: string;
  dockerContextPath?: string;
  componentId?: string;
  openAPIPath?: string;
  libPath?: string;
  secretRef?: string;
  buildpackId?: string;
  testRunnerType?: string;
  isService?: boolean;
  isPublicRepo?: boolean;
  isGitProxy?: boolean;
}

export interface RepositoryMetadata {
  isBareRepo: boolean;
  isSubPathValid: boolean;
  isValidRepo: boolean;
  isSubPathEmpty: boolean;
  hasBallerinaTomlInPath: boolean;
  hasBallerinaTomlInRoot: boolean;
  hasDockerfileInPath: boolean;
  isDockerfilePathValid: boolean;
  isDockerContextPathValid: boolean;
  hasOpenApiFileInPath: boolean;
  hasPomXmlInPath: boolean;
  hasPomXmlInRoot: boolean;
  isOpenApiFilePathValid: boolean;
  isLibPathValid: boolean;
  isBuildpackPathValid: boolean;
  buildpackPath: string;
  isProcfileExists: boolean;
  isTestRunnerPathValid: boolean;
  isEndpointYamlExists: boolean;
}

// Service Component Creation interfaces
export interface ServiceBuildConfig {
  languageVersion?: string;
  port?: number;
  runCommand?: string;
  dockerfilePath?: string;
  envValues?: Array<{ key: string; value: string }>;
}

export interface ServiceCreationData {
  repositoryUrl: string;
  branch: string;
  componentDirectory?: string;
  displayName: string;
  componentName: string;
  description?: string;
  buildPreset: string;
  buildConfig: ServiceBuildConfig;
  orgId: number;
  orgHandler: string;
  projectId: string;
}

export interface ComponentCreationResponse {
  id: string;
  name: string;
  status: string;
  message?: string;
}

export interface ChoreoApiService {
  /**
   * Fetches organization data from the Choreo API
   */
  getOrganizations(options: {
    credentials: BackstageCredentials;
  }): Promise<ChoreoOrg[]>;

  /**
   * Fetches project data from the Choreo API using GraphQL
   */
  getProjects(options: {
    credentials: BackstageCredentials;
    orgId: number;
    orgHandler: string;
  }): Promise<ChoreoProject[]>;

  /**
   * Checks if project creation is allowed for the organization
   */
  getProjectCreationEligibility(options: {
    credentials: BackstageCredentials;
    orgId: number;
    orgHandler: string;
  }): Promise<ProjectCreationEligibility>;

  /**
   * Fetches components for a specific project using GraphQL
   */
  getComponents(options: {
    credentials: BackstageCredentials;
    orgHandler: string;
    projectId: string;
  }): Promise<ChoreoComponent[]>;

  /**
   * Fetches environments for a specific project using GraphQL
   */
  getEnvironments(options: {
    credentials: BackstageCredentials;
    orgUuid: string;
    projectId: string;
    type?: string;
  }): Promise<ChoreoEnvironment[]>;

  /**
   * Validates repository metadata including Dockerfile scanning
   */
  validateRepository(options: RepositoryValidationOptions): Promise<RepositoryMetadata>;

  /**
   * Creates a new web app component in Choreo
   */
  createWebAppComponent(options: {
    credentials: BackstageCredentials;
    data: WebAppCreationData;
  }): Promise<ComponentCreationResponse>;

  /**
   * Creates a new service component in Choreo
   */
  createServiceComponent(options: {
    credentials: BackstageCredentials;
    data: ServiceCreationData;
  }): Promise<ComponentCreationResponse>;
}
