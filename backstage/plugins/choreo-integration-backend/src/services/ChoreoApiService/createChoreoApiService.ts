import { LoggerService } from '@backstage/backend-plugin-api';
import { Config } from '@backstage/config';
import { NotFoundError, ServiceUnavailableError } from '@backstage/errors';
import { ChoreoApiService, ChoreoOrg, ChoreoProject, ProjectCreationEligibility, ChoreoComponent, ChoreoEnvironment, WebAppCreationData, ServiceCreationData, ComponentCreationResponse } from './types';
import { getPresetById } from '../../../../choreo-integration/src/components/ChoreoCreateWebAppPage/buildPresets';
import { getServicePresetById } from '../../../../choreo-integration/src/components/ChoreoCreateServicePage/serviceBuildPresets';

// Buildpack ID mapping - these are the actual UUIDs used by Choreo
const BUILDPACK_ID_MAPPING: Record<string, string> = {
  'nodejs': 'F9E4820E-6284-11EE-8C99-0242AC120004',
  'python': 'F9E4820E-6284-11EE-8C99-0242AC120005', // Example UUID - needs to be verified
  'java': 'F9E4820E-6284-11EE-8C99-0242AC120006', // Example UUID - needs to be verified
  'go': 'F9E4820E-6284-11EE-8C99-0242AC120007', // Example UUID - needs to be verified
  'dotnet': 'F9E4820E-6284-11EE-8C99-0242AC120008', // Example UUID - needs to be verified
  'ballerina': 'F9E4820E-6284-11EE-8C99-0242AC120009', // Example UUID - needs to be verified
  'php': 'F9E4820E-6284-11EE-8C99-0242AC120010', // Example UUID - needs to be verified
  'ruby': 'F9E4820E-6284-11EE-8C99-0242AC120011', // Example UUID - needs to be verified
  'docker': 'F9E4820E-6284-11EE-8C99-0242AC120012', // Example UUID - needs to be verified
  'wso2-mi': 'F9E4820E-6284-11EE-8C99-0242AC120013', // Example UUID - needs to be verified
  'prism-mock': 'F9E4820E-6284-11EE-8C99-0242AC120014', // Example UUID - needs to be verified
};

export async function createChoreoApiService({
  logger,
  config,
}: {
  logger: LoggerService;
  config: Config;
}): Promise<ChoreoApiService> {
  logger.info('Initializing ChoreoApiService');

  // Get configuration values
  const apiUrl = config.getString('choreo.apiUrl');
  const projectApiUrl = config.getString('choreo.projectApiUrl');
  const bearerToken = config.getString('choreo.bearerToken');

  if (!apiUrl || !projectApiUrl || !bearerToken) {
    throw new Error(
      'Choreo API configuration is missing. Please ensure choreo.apiUrl, choreo.projectApiUrl and choreo.bearerToken are configured.',
    );
  }

  return {
    async getOrganizations(_options) {
      logger.info('Fetching organizations from Choreo API');

      try {
        const response = await fetch(`${apiUrl}/orgs`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${bearerToken}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
        });

        if (!response.ok) {
          if (response.status === 404) {
            throw new NotFoundError('Choreo API endpoint not found');
          }
          if (response.status >= 500) {
            throw new ServiceUnavailableError(
              `Choreo API service unavailable: ${response.statusText}`,
            );
          }
          throw new Error(
            `Choreo API request failed: ${response.status} ${response.statusText}`,
          );
        }

        const data: ChoreoOrg[] = await response.json();

        logger.info('Successfully fetched organizations from Choreo API', {
          count: data.length,
        });

        return data;
      } catch (error) {
        if (error instanceof NotFoundError || error instanceof ServiceUnavailableError) {
          throw error;
        }

        logger.error('Failed to fetch organizations from Choreo API', error instanceof Error ? error : new Error(String(error)));
        throw new ServiceUnavailableError(
          'Unable to connect to Choreo API. Please try again later.',
        );
      }
    },

    async getProjects(options) {
      logger.info('Fetching projects from Choreo API', {
        orgId: options.orgId,
        orgHandler: options.orgHandler,
      });

      try {
        const graphqlQuery = {
          query: `query{projects(orgId: ${options.orgId}){
            id, orgId, name, version, createdDate, handler, region, description,
            defaultDeploymentPipelineId,deploymentPipelineIds,
            type, gitProvider, gitOrganization, repository, branch,
            secretRef, updatedAt
          }}`,
        };

        const response = await fetch(`${projectApiUrl}/graphql`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${bearerToken}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          body: JSON.stringify(graphqlQuery),
        });

        if (!response.ok) {
          if (response.status === 404) {
            throw new NotFoundError('Choreo GraphQL API endpoint not found');
          }
          if (response.status >= 500) {
            throw new ServiceUnavailableError(
              `Choreo API service unavailable: ${response.statusText}`,
            );
          }
          throw new Error(
            `Choreo API request failed: ${response.status} ${response.statusText}`,
          );
        }

        const result = await response.json();
        const projects: ChoreoProject[] = result?.data?.projects || [];

        logger.info('Successfully fetched projects from Choreo API', {
          count: projects.length,
        });

        return projects;
      } catch (error) {
        if (error instanceof NotFoundError || error instanceof ServiceUnavailableError) {
          throw error;
        }

        logger.error('Failed to fetch projects from Choreo API', error instanceof Error ? error : new Error(String(error)));
        throw new ServiceUnavailableError(
          'Unable to connect to Choreo API. Please try again later.',
        );
      }
    },

    async getProjectCreationEligibility(options) {
      logger.info('Checking project creation eligibility from Choreo API', {
        orgId: options.orgId,
        orgHandler: options.orgHandler,
      });

      try {
        const graphqlQuery = {
          query: `query{projectCreationEligibility(orgId: ${options.orgId}, orgHandler: "${options.orgHandler}"){
            isProjectCreationAllowed
          }}`,
        };

        const response = await fetch(`${projectApiUrl}/graphql`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${bearerToken}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          body: JSON.stringify(graphqlQuery),
        });

        if (!response.ok) {
          if (response.status === 404) {
            throw new NotFoundError('Choreo GraphQL API endpoint not found');
          }
          if (response.status >= 500) {
            throw new ServiceUnavailableError(
              `Choreo API service unavailable: ${response.statusText}`,
            );
          }
          throw new Error(
            `Choreo API request failed: ${response.status} ${response.statusText}`,
          );
        }

        const result = await response.json();
        const eligibility: ProjectCreationEligibility = result?.data?.projectCreationEligibility || { isProjectCreationAllowed: false };

        logger.info('Successfully checked project creation eligibility from Choreo API', {
          isAllowed: eligibility.isProjectCreationAllowed,
        });

        return eligibility;
      } catch (error) {
        if (error instanceof NotFoundError || error instanceof ServiceUnavailableError) {
          throw error;
        }

        logger.error('Failed to check project creation eligibility from Choreo API', error instanceof Error ? error : new Error(String(error)));
        throw new ServiceUnavailableError(
          'Unable to connect to Choreo API. Please try again later.',
        );
      }
    },

    async getComponents(options) {
      logger.info('Fetching components from Choreo API', {
        orgHandler: options.orgHandler,
        projectId: options.projectId,
      });

      try {
        const graphqlQuery = {
          query: `query{components(orgHandler: "${options.orgHandler}", projectId: "${options.projectId}"){
            projectId, id, description, status, initStatus, name, handler, displayName,
            displayType, version, createdAt, lastBuildDate, orgHandler, isSystemComponent,
            repository {
              buildpackConfig { versionId, buildContext, languageVersion, buildpack { id, language } },
              byocWebAppBuildConfig { id, dockerContext, webAppType }
            },
            componentSubType,
            apiVersions { apiVersion, proxyName, proxyUrl, proxyId, id, state, latest, branch, accessibility },
            deploymentTracks { id, createdAt, updatedAt, apiVersion, branch, description, componentId, latest, versionStrategy, autoDeployEnabled }
          }}`,
        };

        const response = await fetch(`${projectApiUrl}/graphql`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${bearerToken}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          body: JSON.stringify(graphqlQuery),
        });

        if (!response.ok) {
          if (response.status === 404) {
            throw new NotFoundError('Choreo GraphQL API endpoint not found');
          }
          if (response.status >= 500) {
            throw new ServiceUnavailableError(
              `Choreo API service unavailable: ${response.statusText}`,
            );
          }
          throw new Error(
            `Choreo API request failed: ${response.status} ${response.statusText}`,
          );
        }

        const result = await response.json();
        const components: ChoreoComponent[] = result?.data?.components || [];

        logger.info('Successfully fetched components from Choreo API', {
          count: components.length,
        });

        // Log each component for debugging
        components.forEach((component, index) => {
          logger.info(`Component ${index + 1}: ${component.handler} (${component.name}) - Status: ${component.status}, InitStatus: ${component.initStatus}`);
        });

        return components;
      } catch (error) {
        if (error instanceof NotFoundError || error instanceof ServiceUnavailableError) {
          throw error;
        }

        logger.error('Failed to fetch components from Choreo API', error instanceof Error ? error : new Error(String(error)));
        throw new ServiceUnavailableError(
          'Unable to connect to Choreo API. Please try again later.',
        );
      }
    },

    async getEnvironments(options) {
      logger.info('Fetching environments from Choreo API', {
        orgUuid: options.orgUuid,
        projectId: options.projectId,
        type: options.type || 'external',
      });

      try {
        const graphqlQuery = {
          query: `query{environments(orgUuid: "${options.orgUuid}", type: "${options.type || 'external'}", projectId: "${options.projectId}"){
            name, id, choreoEnv, vhost, apiEnvName, isMigrating, apimEnvId, namespace,
            sandboxVhost, critical, isPdp, promoteFrom, dpId, templateId, scaleToZeroEnabled
          }}`,
        };

        const response = await fetch(`${projectApiUrl}/graphql`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${bearerToken}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          body: JSON.stringify(graphqlQuery),
        });

        if (!response.ok) {
          if (response.status === 404) {
            throw new NotFoundError('Choreo GraphQL API endpoint not found');
          }
          if (response.status >= 500) {
            throw new ServiceUnavailableError(
              `Choreo API service unavailable: ${response.statusText}`,
            );
          }
          throw new Error(
            `Choreo API request failed: ${response.status} ${response.statusText}`,
          );
        }

        const result = await response.json();
        const environments: ChoreoEnvironment[] = result?.data?.environments || [];

        logger.info('Successfully fetched environments from Choreo API', {
          count: environments.length,
        });

        return environments;
      } catch (error) {
        if (error instanceof NotFoundError || error instanceof ServiceUnavailableError) {
          throw error;
        }

        logger.error('Failed to fetch environments from Choreo API', error instanceof Error ? error : new Error(String(error)));
        throw new ServiceUnavailableError(
          'Unable to connect to Choreo API. Please try again later.',
        );
      }
    },

    async validateRepository(options) {
      logger.info('Validating repository metadata', {
        organizationName: options.organizationName,
        repoName: options.repoName,
        branch: options.branch,
        subPath: options.subPath,
        dockerFilePath: options.dockerFilePath,
        dockerContextPath: options.dockerContextPath,
      });

      try {
        // Build the GraphQL query with proper parameter handling
        const queryParams = [
          `organizationName: "${options.organizationName}"`,
          `repoName: "${options.repoName}"`,
          `branch: "${options.branch}"`
        ];

        // Add optional parameters only if they have values
        if (options.subPath) {
          queryParams.push(`subPath: "${options.subPath}"`);
        }
        if (options.dockerFilePath) {
          queryParams.push(`dockerFilePath: "${options.dockerFilePath}"`);
        }
        if (options.dockerContextPath) {
          queryParams.push(`dockerContextPath: "${options.dockerContextPath}"`);
        }
        if (options.componentId) {
          queryParams.push(`componentId: "${options.componentId}"`);
        }
        if (options.openAPIPath) {
          queryParams.push(`openAPIPath: "${options.openAPIPath}"`);
        }
        if (options.libPath) {
          queryParams.push(`libPath: "${options.libPath}"`);
        }
        if (options.secretRef) {
          queryParams.push(`secretRef: "${options.secretRef}"`);
        }
        if (options.buildpackId) {
          queryParams.push(`buildpackId: "${options.buildpackId}"`);
        }
        if (options.testRunnerType) {
          queryParams.push(`testRunnerType: "${options.testRunnerType}"`);
        }
        if (options.isService !== undefined) {
          queryParams.push(`isService: ${options.isService}`);
        }
        if (options.isPublicRepo !== undefined) {
          queryParams.push(`isPublicRepo: ${options.isPublicRepo}`);
        }
        if (options.isGitProxy !== undefined) {
          queryParams.push(`isGitProxy: ${options.isGitProxy}`);
        }

        const graphqlQuery = {
          query: `query {
            repoMetadata(${queryParams.join(', ')}) {
              isBareRepo
              isSubPathValid
              isValidRepo
              isSubPathEmpty
              hasBallerinaTomlInPath
              hasBallerinaTomlInRoot
              hasDockerfileInPath
              isDockerfilePathValid
              isDockerContextPathValid
              hasOpenApiFileInPath
              hasPomXmlInPath
              hasPomXmlInRoot
              isOpenApiFilePathValid
              isLibPathValid
              isBuildpackPathValid
              buildpackPath
              isProcfileExists
              isTestRunnerPathValid
              isEndpointYamlExists
            }
          }`,
        };

        logger.debug('GraphQL query for repository validation', {
          query: graphqlQuery.query,
        });

        const response = await fetch(`${projectApiUrl}/graphql`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${bearerToken}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          body: JSON.stringify(graphqlQuery),
        });

        if (!response.ok) {
          const errorText = await response.text();
          logger.error('Repository validation HTTP error', {
            status: response.status,
            statusText: response.statusText,
            errorBody: errorText,
          });

          if (response.status === 404) {
            throw new NotFoundError('Choreo GraphQL API endpoint not found');
          }
          if (response.status >= 500) {
            throw new ServiceUnavailableError(
              `Choreo API service unavailable: ${response.statusText}`,
            );
          }
          throw new Error(
            `Repository validation failed: ${response.status} ${response.statusText}`,
          );
        }

        const result = await response.json();
        if (result.errors) {
          logger.error('Repository validation GraphQL errors', result.errors);
          throw new Error(`Repository validation errors: ${result.errors.map((err: any) => err.message).join(', ')}`);
        }

        const metadata = result?.data?.repoMetadata;
        if (!metadata) {
          throw new Error('No repository metadata returned from validation');
        }

        logger.info('Repository validation completed', {
          isValidRepo: metadata.isValidRepo,
          hasDockerfileInPath: metadata.hasDockerfileInPath,
          isDockerfilePathValid: metadata.isDockerfilePathValid,
          isDockerContextPathValid: metadata.isDockerContextPathValid,
        });

        return metadata;
      } catch (error) {
        if (error instanceof NotFoundError || error instanceof ServiceUnavailableError) {
          throw error;
        }

        logger.error('Failed to validate repository', error instanceof Error ? error : new Error(String(error)));
        throw new ServiceUnavailableError(
          'Unable to validate repository. Please try again later.',
        );
      }
    },

    async createWebAppComponent(options) {
      logger.info('Creating web app component in Choreo', {
        orgHandler: options.data.orgHandler,
        projectId: options.data.projectId,
        componentName: options.data.componentName,
        buildPreset: options.data.buildPreset,
      });

      try {
        const { data } = options;

        // Extract repository information for validation
        const repoUrlMatch = data.repositoryUrl.match(/github\.com\/([^\/]+)\/([^\/]+?)(?:\.git)?$/);
        if (!repoUrlMatch) {
          throw new Error('Invalid GitHub repository URL format');
        }
        const [, organizationName, repoName] = repoUrlMatch;

        // Validate repository before creating component (only for Docker builds for now)
        if (data.buildPreset === 'docker') {
          logger.info('Validating repository for Docker build', {
            organizationName,
            repoName,
            branch: data.branch,
            subPath: data.componentDirectory,
          });

          try {
            const repoMetadata = await this.validateRepository({
              credentials: options.credentials,
              organizationName,
              repoName,
              branch: data.branch,
              subPath: data.componentDirectory,
              dockerFilePath: data.buildConfig.dockerfilePath,
              dockerContextPath: data.componentDirectory || data.componentName,
              isService: false,
              isPublicRepo: true,
              buildpackId: data.buildPreset,
            });

            // Check if repository validation passed
            if (!repoMetadata.isValidRepo) {
              throw new Error('Repository validation failed: Repository is not valid or accessible');
            }

            // For Docker builds, validate Dockerfile presence and validity
            if (!repoMetadata.hasDockerfileInPath) {
              throw new Error('Dockerfile not found in the specified path. Please ensure your repository contains a Dockerfile.');
            }
            if (!repoMetadata.isDockerfilePathValid) {
              throw new Error('Dockerfile path is invalid. Please check the Dockerfile location and try again.');
            }
            if (!repoMetadata.isDockerContextPathValid) {
              throw new Error('Docker context path is invalid. Please verify the Docker context configuration.');
            }

            logger.info('Repository validation passed for Docker build', {
              isValidRepo: repoMetadata.isValidRepo,
              hasDockerfileInPath: repoMetadata.hasDockerfileInPath,
              isDockerfilePathValid: repoMetadata.isDockerfilePathValid,
            });
          } catch (validationError) {
            logger.warn('Repository validation failed, proceeding without validation', {
              error: validationError instanceof Error ? validationError.message : String(validationError),
            });
            // For now, we'll proceed without validation if it fails
            // This ensures backward compatibility while we debug the validation
          }
        } else {
          logger.info('Skipping repository validation for non-Docker build', {
            buildPreset: data.buildPreset,
          });
        }

        const graphqlMutation = `
          mutation CreateByocComponent($component: ByocCreateComponentSchema!) {
            createByocComponent(component: $component) {
              id
              createdAt
              updatedAt
              name
              handle
              organizationId
              projectId
              orgHandle
              type
              description
              imageRegistryId
              imageRegistry {
                id
                createdAt
                updatedAt
                cloudConnectorId
                imageRepositoryName
              }
              componentType
              httpBased
            }
          }
        `;

        const variables = {
          component: {
            name: data.componentName,
            displayName: data.displayName,
            description: data.description || data.componentName,
            orgId: data.orgId,
            orgHandler: data.orgHandler,
            projectId: data.projectId,
            labels: '',
            componentType: 'byocWebAppsDockerfileLess',
            accessibility: 'external',
            byocWebAppsConfig: {
              dockerContext: data.componentDirectory || data.componentName,
              srcGitRepoUrl: data.repositoryUrl,
              srcGitRepoBranch: data.branch,
              webAppType: getPresetById(data.buildPreset)?.name || data.buildPreset, // Use the name of the preset
              webAppBuildCommand: data.buildConfig.buildCommand || '',
              webAppPackageManagerVersion: data.buildConfig.nodeVersion || '',
              webAppOutputDirectory: data.buildConfig.buildPath || '',
              isAppGatewayEnabled: true,
            },
            secretRef: '',
            isPublicRepo: true,
          },
        };

        const response = await fetch(`${projectApiUrl}/graphql`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${bearerToken}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          body: JSON.stringify({
            query: graphqlMutation,
            variables: variables,
          }),
        });

        if (!response.ok) {
          if (response.status === 404) {
            throw new NotFoundError('Choreo GraphQL API endpoint not found');
          }
          if (response.status >= 500) {
            throw new ServiceUnavailableError(
              `Choreo API service unavailable: ${response.statusText}`,
            );
          }
          throw new Error(
            `Choreo API request failed: ${response.status} ${response.statusText}`,
          );
        }

        const result = await response.json();
        if (result.errors) {
          logger.error('Choreo API GraphQL errors', result.errors);
          throw new Error(`GraphQL errors: ${result.errors.map((err: any) => err.message).join(', ')}`);
        }

        const componentCreationResponse: ComponentCreationResponse = {
          id: result?.data?.createByocComponent?.id || 'unknown',
          name: result?.data?.createByocComponent?.name || data.componentName,
          status: 'created', // Assuming 'created' if no errors
          message: 'Web app component created successfully',
        };

        logger.info('Successfully created web app component', {
          componentId: componentCreationResponse.id,
          componentName: componentCreationResponse.name,
          status: componentCreationResponse.status,
        });

        return componentCreationResponse;
      } catch (error) {
        logger.error('Failed to create web app component', error instanceof Error ? error : new Error(String(error)));
        throw new ServiceUnavailableError(
          'Unable to create web app component. Please try again later.',
        );
      }
    },

    async createServiceComponent(options) {
      logger.info('Creating service component in Choreo', {
        orgHandler: options.data.orgHandler,
        projectId: options.data.projectId,
        componentName: options.data.componentName,
        buildPreset: options.data.buildPreset,
      });

      try {
        const { data } = options;

        // Extract repository information for validation
        const repoUrlMatch = data.repositoryUrl.match(/github\.com\/([^\/]+)\/([^\/]+?)(?:\.git)?$/);
        if (!repoUrlMatch) {
          throw new Error('Invalid GitHub repository URL format');
        }
        const [, organizationName, repoName] = repoUrlMatch;

        // Validate repository before creating component (only for Docker builds for now)
        if (data.buildPreset === 'docker') {
          logger.info('Validating repository for Docker service build', {
            organizationName,
            repoName,
            branch: data.branch,
            subPath: data.componentDirectory,
          });

          try {
            const repoMetadata = await this.validateRepository({
              credentials: options.credentials,
              organizationName,
              repoName,
              branch: data.branch,
              subPath: data.componentDirectory,
              dockerFilePath: data.buildConfig.dockerfilePath,
              dockerContextPath: data.componentDirectory || data.componentName,
              isService: true,
              isPublicRepo: true,
              buildpackId: data.buildPreset,
            });

            // Check if repository validation passed
            if (!repoMetadata.isValidRepo) {
              throw new Error('Repository validation failed: Repository is not valid or accessible');
            }

            // For Docker builds, validate Dockerfile presence and validity
            if (!repoMetadata.hasDockerfileInPath) {
              throw new Error('Dockerfile not found in the specified path. Please ensure your repository contains a Dockerfile.');
            }
            if (!repoMetadata.isDockerfilePathValid) {
              throw new Error('Dockerfile path is invalid. Please check the Dockerfile location and try again.');
            }
            if (!repoMetadata.isDockerContextPathValid) {
              throw new Error('Docker context path is invalid. Please verify the Docker context configuration.');
            }

            logger.info('Repository validation passed for Docker service build', {
              isValidRepo: repoMetadata.isValidRepo,
              hasDockerfileInPath: repoMetadata.hasDockerfileInPath,
              isDockerfilePathValid: repoMetadata.isDockerfilePathValid,
            });
          } catch (validationError) {
            logger.warn('Repository validation failed for service, proceeding without validation', {
              error: validationError instanceof Error ? validationError.message : String(validationError),
            });
            // For now, we'll proceed without validation if it fails
            // This ensures backward compatibility while we debug the validation
          }
        } else {
          logger.info('Skipping repository validation for non-Docker service build', {
            buildPreset: data.buildPreset,
          });
        }

        const servicePreset = getServicePresetById(data.buildPreset);

        const graphqlMutation = `
          mutation CreateBuildpackComponent($component: ByocCreateComponentSchema!) {
            createBuildpackComponent(component: $component) {
              id
              createdAt
              updatedAt
              name
              handle
              organizationId
              projectId
              orgHandle
              type
              description
              imageRegistryId
              imageRegistry {
                id
                createdAt
                updatedAt
                cloudConnectorId
                imageRepositoryName
              }
              componentType
              httpBased
            }
          }
        `;

        // Get the actual buildpack UUID
        const buildpackId = BUILDPACK_ID_MAPPING[servicePreset?.buildpackId || data.buildPreset] ||
                           BUILDPACK_ID_MAPPING['nodejs']; // Default to Node.js

        logger.info('Buildpack ID resolution', {
          requestedPreset: data.buildPreset,
          servicePresetBuildpackId: servicePreset?.buildpackId,
          resolvedBuildpackId: buildpackId,
          availableMappings: Object.keys(BUILDPACK_ID_MAPPING),
        });

        const variables = {
          component: {
            name: data.componentName,
            displayName: data.displayName,
            description: data.description || '',
            orgId: data.orgId,
            orgHandler: data.orgHandler,
            projectId: data.projectId,
            labels: '',
            componentType: 'buildpackService',
            port: null,
            oasFilePath: '',
            accessibility: 'external',
            isAsyncCreationEnabled: true,
            buildpackConfig: {
              buildContext: (data.componentDirectory || data.componentName || 'app').replace(/^\/+/, ''),
              srcGitRepoUrl: data.repositoryUrl,
              srcGitRepoBranch: data.branch,
              languageVersion: data.buildConfig.languageVersion || servicePreset?.defaultConfig.languageVersion || '20.x.x',
              buildpackId: buildpackId,
            },
            secretRef: '',
            isPublicRepo: true,
          },
        };

        // Validate required fields
        const requiredFields = {
          'name': variables.component.name,
          'displayName': variables.component.displayName,
          'description': variables.component.description,
          'orgId': variables.component.orgId,
          'orgHandler': variables.component.orgHandler,
          'projectId': variables.component.projectId,
          'componentType': variables.component.componentType,
          'buildpackConfig.buildContext': variables.component.buildpackConfig.buildContext,
          'buildpackConfig.buildpackId': variables.component.buildpackConfig.buildpackId,
        };

        const missingFields = Object.entries(requiredFields)
          .filter(([_, value]) => !value || value === '')
          .map(([field, _]) => field);

        if (missingFields.length > 0) {
          logger.error('Missing required fields for service creation', {
            missingFields,
            variables: JSON.stringify(variables, null, 2),
          });
          throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
        }

        logger.info('Sending GraphQL mutation to Choreo API', {
          mutation: 'CreateBuildpackComponent',
          endpoint: `${projectApiUrl}/graphql`,
          variables: JSON.stringify(variables, null, 2),
          buildpackId: buildpackId,
          servicePreset: servicePreset?.id,
        });

        // Log comparison with working Postman example
        logger.info('Payload comparison with working example', {
          current: {
            name: variables.component.name,
            displayName: variables.component.displayName,
            description: variables.component.description,
            buildContext: variables.component.buildpackConfig.buildContext,
            languageVersion: variables.component.buildpackConfig.languageVersion,
            buildpackId: variables.component.buildpackConfig.buildpackId,
          },
          workingExample: {
            name: 'reading-books-list-servic',
            displayName: 'reading-books-list-service-nodejs',
            description: '',
            buildContext: 'reading-books-list-service-nodejs',
            languageVersion: '20.x.x',
            buildpackId: 'F9E4820E-6284-11EE-8C99-0242AC120004',
          },
        });

        const response = await fetch(`${projectApiUrl}/graphql`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${bearerToken}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          body: JSON.stringify({
            query: graphqlMutation,
            variables,
          }),
        });

        if (!response.ok) {
          const errorText = await response.text();
          logger.error('Choreo API HTTP error', {
            status: response.status,
            statusText: response.statusText,
            errorBody: errorText,
            requestVariables: JSON.stringify(variables, null, 2),
          });
          if (response.status === 404) {
            throw new NotFoundError('Choreo GraphQL API endpoint not found');
          }
          if (response.status >= 500) {
            throw new ServiceUnavailableError(
              `Choreo API service unavailable: ${response.statusText}`,
            );
          }
          throw new Error(`HTTP error! status: ${response.status}, body: ${errorText}`);
        }

        const result = await response.json();
        if (result.errors) {
          logger.error('Choreo API GraphQL errors', {
            errors: result.errors,
            requestVariables: JSON.stringify(variables, null, 2),
            mutation: 'CreateBuildpackComponent',
          });
          throw new Error(`GraphQL errors: ${result.errors.map((err: any) => err.message).join(', ')}`);
        }

        const componentCreationResponse: ComponentCreationResponse = {
          id: result?.data?.createBuildpackComponent?.id || 'unknown',
          name: result?.data?.createBuildpackComponent?.name || data.componentName,
          status: 'created',
          message: 'Service component created successfully',
        };

        logger.info('Successfully created service component', {
          componentId: componentCreationResponse.id,
          componentName: componentCreationResponse.name,
          status: componentCreationResponse.status,
        });

        return componentCreationResponse;
      } catch (error) {
        logger.error('Failed to create service component', error instanceof Error ? error : new Error(String(error)));
        throw new ServiceUnavailableError(
          'Unable to create service component. Please try again later.',
        );
      }
    },
  };
}
