import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { TestApiProvider, TestApiRegistry } from '@backstage/test-utils';
import { MemoryRouter } from 'react-router-dom';
import { ChoreoComponentsComponent } from './ChoreoComponentsComponent';
import { choreoApiRef, ChoreoComponent, ChoreoEnvironment } from '../../api';
import { errorApiRef } from '@backstage/core-plugin-api';
import { createComponentRouteRef } from '../../routes';

const mockChoreoApi = {
  getComponents: jest.fn(),
  getEnvironments: jest.fn(),
  getOrganizations: jest.fn(),
  getProjects: jest.fn(),
  getProjectCreationEligibility: jest.fn(),
  getDefaultOrganization: jest.fn(),
};

const mockErrorApi = {
  post: jest.fn(),
  error$: jest.fn(),
};

// Mock navigation
const mockNavigate = jest.fn();
const mockUseRouteRef = jest.fn();

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

jest.mock('@backstage/core-plugin-api', () => ({
  ...jest.requireActual('@backstage/core-plugin-api'),
  useRouteRef: (routeRef: any) => mockUseRouteRef(routeRef),
}));

const mockComponents: ChoreoComponent[] = [
  {
    projectId: 'test-project-1',
    id: 'component-1',
    description: 'Test component 1',
    status: 'active',
    initStatus: 'ready',
    name: 'test-component-1',
    handler: 'test-handler-1',
    displayName: 'Test Component 1',
    displayType: 'Service',
    version: '1.0.0',
    createdAt: '2023-01-01T00:00:00Z',
    lastBuildDate: '2023-01-02T00:00:00Z',
    orgHandler: 'test-org',
    isSystemComponent: false,
    componentSubType: 'REST API',
  },
  {
    projectId: 'test-project-1',
    id: 'component-2',
    description: 'Test component 2',
    status: 'pending',
    initStatus: 'pending',
    name: 'test-component-2',
    handler: 'test-handler-2',
    displayName: 'Test Component 2',
    displayType: 'Web Application',
    version: '2.0.0',
    createdAt: '2023-01-03T00:00:00Z',
    orgHandler: 'test-org',
    isSystemComponent: false,
    componentSubType: 'React App',
  },
];

const mockEnvironments: ChoreoEnvironment[] = [
  {
    name: 'Development',
    id: 'env-1',
    choreoEnv: 'dev',
    vhost: 'dev.example.com',
    apiEnvName: 'dev-api',
    isMigrating: false,
    apimEnvId: 'apim-dev',
    namespace: 'dev-namespace',
    sandboxVhost: 'sandbox-dev.example.com',
    critical: false,
    isPdp: false,
    dpId: 'dp-1',
    templateId: 'template-1',
    scaleToZeroEnabled: true,
  },
];

describe('ChoreoComponentsComponent', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseRouteRef.mockImplementation((routeRef) => {
      if (routeRef === createComponentRouteRef) {
        return ({ orgHandler, projectId }: any) => `/create/${orgHandler}/${projectId}`;
      }
      return () => '/';
    });
  });

  it('renders loading state initially', () => {
    mockChoreoApi.getComponents.mockReturnValue(new Promise(() => {}));
    mockChoreoApi.getEnvironments.mockReturnValue(new Promise(() => {}));

    const apis = TestApiRegistry.from([
      [choreoApiRef, mockChoreoApi],
      [errorApiRef, mockErrorApi],
    ]);

    render(
      <MemoryRouter>
        <TestApiProvider apis={apis}>
          <ChoreoComponentsComponent
            orgHandler="test-org"
            projectId="test-project-1"
          />
        </TestApiProvider>
      </MemoryRouter>,
    );

    expect(screen.getByTestId('progress')).toBeInTheDocument();
  });

  it('renders components table when data is loaded', async () => {
    mockChoreoApi.getComponents.mockResolvedValue({
      components: mockComponents,
    });
    mockChoreoApi.getEnvironments.mockResolvedValue({
      environments: mockEnvironments,
    });

    const apis = TestApiRegistry.from([
      [choreoApiRef, mockChoreoApi],
      [errorApiRef, mockErrorApi],
    ]);

    render(
      <MemoryRouter>
        <TestApiProvider apis={apis}>
          <ChoreoComponentsComponent
            orgHandler="test-org"
            projectId="test-project-1"
            orgUuid="test-org-uuid"
            projectName="Test Project"
          />
        </TestApiProvider>
      </MemoryRouter>,
    );

    await waitFor(() => {
      expect(screen.getByText('Components in Test Project')).toBeInTheDocument();
    });

    expect(screen.getByText('Test Component 1')).toBeInTheDocument();
    expect(screen.getByText('Test Component 2')).toBeInTheDocument();
    expect(screen.getByText('Service')).toBeInTheDocument();
    expect(screen.getByText('Web Application')).toBeInTheDocument();
    expect(screen.getByText('Active')).toBeInTheDocument();
    expect(screen.getByText('Pending')).toBeInTheDocument();
  });

  it('renders empty state when no components are found', async () => {
    mockChoreoApi.getComponents.mockResolvedValue({
      components: [],
    });
    mockChoreoApi.getEnvironments.mockResolvedValue({
      environments: [],
    });

    const apis = TestApiRegistry.from([
      [choreoApiRef, mockChoreoApi],
      [errorApiRef, mockErrorApi],
    ]);

    render(
      <MemoryRouter>
        <TestApiProvider apis={apis}>
          <ChoreoComponentsComponent
            orgHandler="test-org"
            projectId="test-project-1"
          />
        </TestApiProvider>
      </MemoryRouter>,
    );

    await waitFor(() => {
      expect(screen.getByText('No components found')).toBeInTheDocument();
    });

    expect(screen.getByText("This project doesn't have any components yet.")).toBeInTheDocument();
  });

  it('renders error state when API call fails', async () => {
    const error = new Error('Failed to fetch components');
    mockChoreoApi.getComponents.mockRejectedValue(error);
    mockChoreoApi.getEnvironments.mockResolvedValue({
      environments: [],
    });

    const apis = TestApiRegistry.from([
      [choreoApiRef, mockChoreoApi],
      [errorApiRef, mockErrorApi],
    ]);

    render(
      <MemoryRouter>
        <TestApiProvider apis={apis}>
          <ChoreoComponentsComponent
            orgHandler="test-org"
            projectId="test-project-1"
          />
        </TestApiProvider>
      </MemoryRouter>,
    );

    await waitFor(() => {
      expect(screen.getByText(/Failed to fetch components/)).toBeInTheDocument();
    });
  });

  it('displays environment information when available', async () => {
    mockChoreoApi.getComponents.mockResolvedValue({
      components: mockComponents,
    });
    mockChoreoApi.getEnvironments.mockResolvedValue({
      environments: mockEnvironments,
    });

    const apis = TestApiRegistry.from([
      [choreoApiRef, mockChoreoApi],
      [errorApiRef, mockErrorApi],
    ]);

    render(
      <TestApiProvider apis={apis}>
        <ChoreoComponentsComponent
          orgHandler="test-org"
          projectId="test-project-1"
          orgUuid="test-org-uuid"
        />
      </TestApiProvider>,
    );

    await waitFor(() => {
      expect(screen.getByText('Environments: Development')).toBeInTheDocument();
    });
  });

  it('calls API with correct parameters', async () => {
    mockChoreoApi.getComponents.mockResolvedValue({
      components: [],
    });
    mockChoreoApi.getEnvironments.mockResolvedValue({
      environments: [],
    });

    const apis = TestApiRegistry.from([
      [choreoApiRef, mockChoreoApi],
      [errorApiRef, mockErrorApi],
    ]);

    render(
      <MemoryRouter>
        <TestApiProvider apis={apis}>
          <ChoreoComponentsComponent
            orgHandler="test-org"
            projectId="test-project-1"
            orgUuid="test-org-uuid"
          />
        </TestApiProvider>
      </MemoryRouter>,
    );

    await waitFor(() => {
      expect(mockChoreoApi.getComponents).toHaveBeenCalledWith('test-org', 'test-project-1');
      expect(mockChoreoApi.getEnvironments).toHaveBeenCalledWith('test-org-uuid', 'test-project-1');
    });
  });

  it('renders create button and handles click', async () => {
    mockChoreoApi.getComponents.mockResolvedValue({
      components: mockComponents,
    });
    mockChoreoApi.getEnvironments.mockResolvedValue({
      environments: mockEnvironments,
    });

    const apis = TestApiRegistry.from([
      [choreoApiRef, mockChoreoApi],
      [errorApiRef, mockErrorApi],
    ]);

    render(
      <MemoryRouter>
        <TestApiProvider apis={apis}>
          <ChoreoComponentsComponent
            orgHandler="test-org"
            projectId="test-project-1"
          />
        </TestApiProvider>
      </MemoryRouter>,
    );

    await waitFor(() => {
      expect(screen.getByText('Create')).toBeInTheDocument();
    });

    const createButton = screen.getByText('Create');
    fireEvent.click(createButton);

    expect(mockNavigate).toHaveBeenCalledWith('/create/test-org/test-project-1');
  });
});
