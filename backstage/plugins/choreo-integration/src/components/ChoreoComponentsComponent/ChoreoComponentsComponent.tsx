import React from 'react';
import { Typo<PERSON>, <PERSON>, Button } from '@material-ui/core';
import { Add as AddIcon } from '@material-ui/icons';
import {
  Table,
  TableColumn,
  Progress,
  ResponseErrorPanel,
  StatusOK,
  StatusError,
  StatusPending,
} from '@backstage/core-components';
import { useApi, useRouteRef } from '@backstage/core-plugin-api';
import useAsync from 'react-use/lib/useAsync';
import { useNavigate } from 'react-router-dom';
import { choreoApiRef, ChoreoComponent, ChoreoEnvironment } from '../../api';
import { createComponentRouteRef } from '../../routes';

interface ChoreoComponentsComponentProps {
  orgHandler: string;
  projectId: string;
  orgUuid?: string;
  projectName?: string;
}

const formatDate = (dateString: string): string => {
  try {
    return new Date(dateString).toLocaleDateString();
  } catch {
    return dateString;
  }
};

const getStatusIcon = (status: string, initStatus?: string) => {
  const normalizedStatus = status?.toLowerCase();
  const normalizedInitStatus = initStatus?.toLowerCase();
  
  if (normalizedStatus === 'active' || normalizedStatus === 'ready') {
    return <StatusOK>Active</StatusOK>;
  }
  if (normalizedStatus === 'failed' || normalizedInitStatus === 'failed') {
    return <StatusError>Failed</StatusError>;
  }
  if (normalizedStatus === 'pending' || normalizedInitStatus === 'pending' || normalizedStatus === 'building') {
    return <StatusPending>Pending</StatusPending>;
  }
  return <Typography variant="body2">{status}</Typography>;
};

export const ChoreoComponentsComponent = ({
  orgHandler,
  projectId,
  orgUuid,
  projectName,
}: ChoreoComponentsComponentProps) => {
  const choreoApi = useApi(choreoApiRef);
  const navigate = useNavigate();
  const createComponentRoute = useRouteRef(createComponentRouteRef);

  const handleCreateComponent = () => {
    navigate(createComponentRoute({ orgHandler, projectId }));
  };

  const { value: componentsData, loading: componentsLoading, error: componentsError } = useAsync(async () => {
    const response = await choreoApi.getComponents(orgHandler, projectId);
    return response.components;
  }, [choreoApi, orgHandler, projectId]);

  const { value: environmentsData, loading: environmentsLoading, error: environmentsError } = useAsync(async () => {
    if (!orgUuid) return [];
    const response = await choreoApi.getEnvironments(orgUuid, projectId);
    return response.environments;
  }, [choreoApi, orgUuid, projectId]);

  if (componentsLoading || environmentsLoading) {
    return <Progress />;
  }

  if (componentsError) {
    return <ResponseErrorPanel error={componentsError} />;
  }

  if (environmentsError) {
    return <ResponseErrorPanel error={environmentsError} />;
  }

  const components = componentsData || [];
  const environments = environmentsData || [];

  const columns: TableColumn[] = [
    {
      title: 'Name',
      field: 'displayName',
      render: (row: Partial<ChoreoComponent>) => (
        <Box>
          <Typography variant="subtitle2">{row.displayName}</Typography>
          <Typography variant="body2" color="textSecondary">
            {row.name} ({row.handler})
          </Typography>
        </Box>
      ),
    },
    {
      title: 'Type',
      field: 'displayType',
      render: (row: Partial<ChoreoComponent>) => (
        <Typography variant="body2">{row.displayType}</Typography>
      ),
    },
    {
      title: 'Status',
      field: 'status',
      render: (row: Partial<ChoreoComponent>) => 
        getStatusIcon(row.status || '', row.initStatus),
    },
    {
      title: 'Version',
      field: 'version',
      render: (row: Partial<ChoreoComponent>) => (
        <Typography variant="body2">{row.version}</Typography>
      ),
    },
    {
      title: 'Created',
      field: 'createdAt',
      render: (row: Partial<ChoreoComponent>) => (
        <Typography variant="body2">
          {row.createdAt ? formatDate(row.createdAt) : 'N/A'}
        </Typography>
      ),
    },
    {
      title: 'Last Build',
      field: 'lastBuildDate',
      render: (row: Partial<ChoreoComponent>) => (
        <Typography variant="body2">
          {row.lastBuildDate ? formatDate(row.lastBuildDate) : 'N/A'}
        </Typography>
      ),
    },
    {
      title: 'Description',
      field: 'description',
      render: (row: Partial<ChoreoComponent>) => (
        <Typography variant="body2" style={{ maxWidth: 200 }}>
          {row.description || 'No description'}
        </Typography>
      ),
    },
  ];

  return (
    <Box>
      <Box mb={2} display="flex" justifyContent="space-between" alignItems="flex-start">
        <Box>
          <Typography variant="h6">
            Components in {projectName || 'Project'}
          </Typography>
          <Typography variant="body2" color="textSecondary">
            Project ID: {projectId} | Organization: {orgHandler}
          </Typography>
          {environments.length > 0 && (
            <Typography variant="body2" color="textSecondary">
              Environments: {environments.map(env => env.name).join(', ')}
            </Typography>
          )}
        </Box>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={handleCreateComponent}
        >
          Create
        </Button>
      </Box>

      <Table
        title={`Components (${components.length})`}
        options={{
          search: true,
          paging: true,
          pageSize: 10,
          pageSizeOptions: [5, 10, 20],
        }}
        columns={columns}
        data={components}
        emptyContent={
          <Box textAlign="center" py={4}>
            <Typography variant="h6" color="textSecondary">
              No components found
            </Typography>
            <Typography variant="body2" color="textSecondary">
              This project doesn't have any components yet.
            </Typography>
          </Box>
        }
      />
    </Box>
  );
};
