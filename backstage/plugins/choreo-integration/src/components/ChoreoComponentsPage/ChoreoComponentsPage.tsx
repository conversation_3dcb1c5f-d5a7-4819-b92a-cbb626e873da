import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button, Box } from '@material-ui/core';
import { ArrowBack } from '@material-ui/icons';
import {
  Content,
  ContentHeader,
  SupportButton,
} from '@backstage/core-components';
import { ChoreoComponentsComponent } from '../ChoreoComponentsComponent';

interface ChoreoComponentsPageParams {
  orgHandler: string;
  projectId: string;
}

export const ChoreoComponentsPage = () => {
  const { orgHandler, projectId } = useParams<ChoreoComponentsPageParams>();
  const navigate = useNavigate();

  if (!orgHandler || !projectId) {
    return (
      <Content>
        <ContentHeader title="Error">
          <SupportButton>Invalid parameters for components page.</SupportButton>
        </ContentHeader>
      </Content>
    );
  }

  const handleBackToProjects = () => {
    navigate('/');
  };

  return (
    <Content>
      <ContentHeader title="Project Components">
        <Box display="flex" alignItems="center" gap={1}>
          <Button
            variant="outlined"
            startIcon={<ArrowBack />}
            onClick={handleBackToProjects}
          >
            Back to Projects
          </Button>
          <SupportButton>
            View and manage components within this Choreo project.
          </SupportButton>
        </Box>
      </ContentHeader>
      <ChoreoComponentsComponent
        orgHandler={orgHandler}
        projectId={projectId}
      />
    </Content>
  );
};
