import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { TestApiProvider, TestApiRegistry } from '@backstage/test-utils';
import { MemoryRouter } from 'react-router-dom';
import { ChoreoComponentsPage } from './ChoreoComponentsPage';
import { choreoApiRef } from '../../api';
import { errorApiRef } from '@backstage/core-plugin-api';

const mockNavigate = jest.fn();

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useParams: () => ({
    orgHandler: 'test-org',
    projectId: 'test-project-1',
  }),
  useNavigate: () => mockNavigate,
}));

const mockChoreoApi = {
  getComponents: jest.fn(),
  getEnvironments: jest.fn(),
  getOrganizations: jest.fn(),
  getProjects: jest.fn(),
  getProjectCreationEligibility: jest.fn(),
  getDefaultOrganization: jest.fn(),
};

const mockErrorApi = {
  post: jest.fn(),
  error$: jest.fn(),
};

describe('ChoreoComponentsPage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockChoreoApi.getComponents.mockResolvedValue({
      components: [],
    });
    mockChoreoApi.getEnvironments.mockResolvedValue({
      environments: [],
    });
  });

  it('renders the page with correct title', async () => {
    const apis = TestApiRegistry.from([
      [choreoApiRef, mockChoreoApi],
      [errorApiRef, mockErrorApi],
    ]);

    render(
      <MemoryRouter>
        <TestApiProvider apis={apis}>
          <ChoreoComponentsPage />
        </TestApiProvider>
      </MemoryRouter>,
    );

    expect(screen.getByText('Project Components')).toBeInTheDocument();
    expect(screen.getByText('Back to Projects')).toBeInTheDocument();
  });

  it('navigates back to projects when back button is clicked', async () => {
    const apis = TestApiRegistry.from([
      [choreoApiRef, mockChoreoApi],
      [errorApiRef, mockErrorApi],
    ]);

    render(
      <MemoryRouter>
        <TestApiProvider apis={apis}>
          <ChoreoComponentsPage />
        </TestApiProvider>
      </MemoryRouter>,
    );

    const backButton = screen.getByText('Back to Projects');
    fireEvent.click(backButton);

    expect(mockNavigate).toHaveBeenCalledWith('/');
  });

  it('renders ChoreoComponentsComponent with correct props', async () => {
    const apis = TestApiRegistry.from([
      [choreoApiRef, mockChoreoApi],
      [errorApiRef, mockErrorApi],
    ]);

    render(
      <MemoryRouter>
        <TestApiProvider apis={apis}>
          <ChoreoComponentsPage />
        </TestApiProvider>
      </MemoryRouter>,
    );

    expect(mockChoreoApi.getComponents).toHaveBeenCalledWith('test-org', 'test-project-1');
  });
});

// Test for invalid parameters
describe('ChoreoComponentsPage with invalid params', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders error when parameters are missing', () => {
    jest.doMock('react-router-dom', () => ({
      ...jest.requireActual('react-router-dom'),
      useParams: () => ({
        orgHandler: undefined,
        projectId: undefined,
      }),
      useNavigate: () => mockNavigate,
    }));

    const { ChoreoComponentsPage: TestPage } = require('./ChoreoComponentsPage');

    const apis = TestApiRegistry.from([
      [choreoApiRef, mockChoreoApi],
      [errorApiRef, mockErrorApi],
    ]);

    render(
      <MemoryRouter>
        <TestApiProvider apis={apis}>
          <TestPage />
        </TestApiProvider>
      </MemoryRouter>,
    );

    expect(screen.getByText('Error')).toBeInTheDocument();
    expect(screen.getByText('Invalid parameters for components page.')).toBeInTheDocument();
  });
});
