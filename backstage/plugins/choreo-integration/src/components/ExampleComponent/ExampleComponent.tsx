import React from 'react';
import { Grid } from '@material-ui/core';
import {
  <PERSON>er,
  Page,
  Content,
  ContentHeader,
  HeaderLabel,
  SupportButton,
  Progress,
  ResponseErrorPanel,
} from '@backstage/core-components';
import { useApi } from '@backstage/core-plugin-api';
import useAsync from 'react-use/lib/useAsync';
import { ChoreoOrgsComponent } from '../ChoreoOrgsComponent';
import { ChoreoProjectsComponent } from '../ChoreoProjectsComponent';
import { choreoApiRef } from '../../api';

export const ExampleComponent = () => {
  const choreoApi = useApi(choreoApiRef);

  const { value: orgsData, loading, error } = useAsync(async () => {
    const response = await choreoApi.getOrganizations();
    return response;
  }, [choreoApi]);

  if (loading) {
    return (
      <Page themeId="tool">
        <Header title="Choreo Integration" subtitle="Loading...">
          <HeaderLabel label="Owner" value="Team X" />
          <HeaderLabel label="Lifecycle" value="Alpha" />
        </Header>
        <Content>
          <Progress />
        </Content>
      </Page>
    );
  }

  if (error) {
    return (
      <Page themeId="tool">
        <Header title="Choreo Integration" subtitle="Error loading data">
          <HeaderLabel label="Owner" value="Team X" />
          <HeaderLabel label="Lifecycle" value="Alpha" />
        </Header>
        <Content>
          <ResponseErrorPanel error={error} />
        </Content>
      </Page>
    );
  }

  const organizations = orgsData?.organizations || [];

  // Get default organization using Enhanced Selection Algorithm
  // For now, we'll use a mock userIdpId - in a real implementation,
  // this would come from the authentication context
  const mockUserIdpId = organizations.length > 0 ? organizations[0].owner.idpId : undefined;

  const defaultOrgResult = choreoApi.getDefaultOrganization(organizations, {
    userIdpId: mockUserIdpId
  });

  const defaultOrg = defaultOrgResult?.organization;

  return (
    <Page themeId="tool">
      <Header title="Choreo Integration" subtitle="View and manage Choreo organizations and projects">
        <HeaderLabel label="Owner" value="Team X" />
        <HeaderLabel label="Lifecycle" value="Alpha" />
      </Header>
      <Content>
        <ContentHeader title="Organizations">
          <SupportButton>
            This plugin integrates with Choreo to display organization information with enhanced default organization selection.
          </SupportButton>
        </ContentHeader>

        <Grid container spacing={3} direction="column">
          <Grid item>
            <ChoreoOrgsComponent />
          </Grid>

          <Grid item>
            <ContentHeader title="Projects">
              <SupportButton>
                Projects from the selected default organization are displayed below.
              </SupportButton>
            </ContentHeader>
            <ChoreoProjectsComponent
              orgId={defaultOrg?.id ? Number(defaultOrg.id) : undefined}
              orgHandler={defaultOrg?.handle}
              organizationName={defaultOrg?.name}
            />
          </Grid>
        </Grid>
      </Content>
    </Page>
  );
};
