
import { ExampleComponent } from './ExampleComponent';
import { rest } from 'msw';
import { setupServer } from 'msw/node';
import { screen } from '@testing-library/react';
import {
  registerMswTestHooks,
  renderInTestApp,
  TestApiProvider,
} from '@backstage/test-utils';
import { choreoApiRef } from '../../api';

const mockChoreoApi = {
  getOrganizations: jest.fn(),
  getDefaultOrganization: jest.fn(),
  getProjects: jest.fn(),
  getProjectCreationEligibility: jest.fn(),
};

describe('ExampleComponent', () => {
  const server = setupServer();
  // Enable sane handlers for network requests
  registerMswTestHooks(server);

  // setup mock response
  beforeEach(() => {
    jest.clearAllMocks();
    mockChoreoApi.getOrganizations.mockResolvedValue({
      organizations: [],
      status: 'success',
      count: 0,
    });
    mockChoreoApi.getDefaultOrganization.mockReturnValue(null);
    mockChoreoApi.getProjects.mockResolvedValue({
      projects: [],
      status: 'success',
      count: 0,
    });
    server.use(
      rest.get('/*', (_, res, ctx) => res(ctx.status(200), ctx.json({}))),
    );
  });

  it('should render', async () => {
    await renderInTestApp(
      <TestApiProvider apis={[[choreoApiRef, mockChoreoApi]]}>
        <ExampleComponent />
      </TestApiProvider>,
    );
    expect(screen.getByText('Choreo Integration')).toBeInTheDocument();
    // Check for both Organizations and Projects sections
    expect(screen.getAllByText('Organizations')).toHaveLength(2); // One in ContentHeader, one in Table
    expect(screen.getAllByText('Projects')).toHaveLength(2); // One in ContentHeader, one in Table
  });
});
