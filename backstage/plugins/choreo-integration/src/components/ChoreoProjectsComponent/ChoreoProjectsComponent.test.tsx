import React from 'react';
import { renderInTestApp, TestApiProvider } from '@backstage/test-utils';
import { ChoreoProjectsComponent } from './ChoreoProjectsComponent';
import { choreoApiRef } from '../../api';

const mockChoreoApi = {
  getOrganizations: jest.fn(),
  getDefaultOrganization: jest.fn(),
  getProjects: jest.fn(),
  getProjectCreationEligibility: jest.fn(),
};

describe('ChoreoProjectsComponent', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders loading state', async () => {
    mockChoreoApi.getProjects.mockImplementation(
      () => new Promise(() => {}), // Never resolves
    );

    const { getByTestId } = await renderInTestApp(
      <TestApiProvider apis={[[choreoApiRef, mockChoreoApi]]}>
        <ChoreoProjectsComponent orgId={74800} orgHandler="testorg" />
      </TestApiProvider>,
    );

    expect(getByTestId('progress')).toBeInTheDocument();
  });

  it('renders project data', async () => {
    const mockProjects = [
      {
        id: '1',
        orgId: '74800',
        name: 'Project 1',
        version: '1.0.0',
        createdDate: '2023-01-01T00:00:00Z',
        handler: 'project1',
        region: 'us-east-1',
        description: 'First project',
        type: 'MONO_REPO',
        updatedAt: '2023-01-01T00:00:00Z',
      },
      {
        id: '2',
        orgId: '74800',
        name: 'Project 2',
        version: '2.0.0',
        createdDate: '2023-01-02T00:00:00Z',
        handler: 'project2',
        region: 'us-west-2',
        description: 'Second project',
        type: 'MULTI_REPO',
        updatedAt: '2023-01-02T00:00:00Z',
      },
    ];

    mockChoreoApi.getProjects.mockResolvedValue({
      projects: mockProjects,
      status: 'success',
      count: 2,
    });

    const { getByText } = await renderInTestApp(
      <TestApiProvider apis={[[choreoApiRef, mockChoreoApi]]}>
        <ChoreoProjectsComponent orgId={74800} orgHandler="testorg" />
      </TestApiProvider>,
    );

    expect(mockChoreoApi.getProjects).toHaveBeenCalledWith(74800, 'testorg');
    expect(getByText('Project 1')).toBeInTheDocument();
    expect(getByText('Project 2')).toBeInTheDocument();
    expect(getByText('First project')).toBeInTheDocument();
    expect(getByText('Second project')).toBeInTheDocument();
    expect(getByText('1.0.0')).toBeInTheDocument();
    expect(getByText('2.0.0')).toBeInTheDocument();
  });

  it('handles error state', async () => {
    const mockError = new Error('Failed to fetch projects');
    mockChoreoApi.getProjects.mockRejectedValue(mockError);

    const { getByText } = await renderInTestApp(
      <TestApiProvider apis={[[choreoApiRef, mockChoreoApi]]}>
        <ChoreoProjectsComponent orgId={74800} orgHandler="testorg" />
      </TestApiProvider>,
    );

    expect(getByText('Failed to fetch projects')).toBeInTheDocument();
  });

  it('renders with organization filter', async () => {
    const mockProjects = [
      {
        id: '1',
        orgId: '123',
        name: 'Org Project',
        version: '1.0.0',
        createdDate: '2023-01-01T00:00:00Z',
        handler: 'orgproject',
        region: 'us-east-1',
        description: 'Project in specific org',
        updatedAt: '2023-01-01T00:00:00Z',
      },
    ];

    mockChoreoApi.getProjects.mockResolvedValue({
      projects: mockProjects,
      status: 'success',
      count: 1,
    });

    const { getByText } = await renderInTestApp(
      <TestApiProvider apis={[[choreoApiRef, mockChoreoApi]]}>
        <ChoreoProjectsComponent
          orgId={123}
          orgHandler="myorg"
          organizationName="My Organization"
        />
      </TestApiProvider>,
    );

    expect(mockChoreoApi.getProjects).toHaveBeenCalledWith(123, 'myorg');
    expect(getByText('Projects in My Organization')).toBeInTheDocument();
    expect(getByText('Org Project')).toBeInTheDocument();
  });

  it('shows empty state when no projects found', async () => {
    mockChoreoApi.getProjects.mockResolvedValue({
      projects: [],
      status: 'success',
      count: 0,
    });

    const { getByText } = await renderInTestApp(
      <TestApiProvider apis={[[choreoApiRef, mockChoreoApi]]}>
        <ChoreoProjectsComponent orgId={123} orgHandler="testorg" />
      </TestApiProvider>,
    );

    expect(getByText('No projects found in this organization')).toBeInTheDocument();
  });
});
