import React from 'react';
import { useApi, useRouteRef } from '@backstage/core-plugin-api';
import { useNavigate } from 'react-router-dom';
import {
  Table,
  TableColumn,
  Progress,
  ResponseErrorPanel,
  InfoCard,
} from '@backstage/core-components';
import useAsync from 'react-use/lib/useAsync';
import { choreoApiRef, ChoreoProject } from '../../api';
import { componentsRouteRef } from '../../routes';

interface ChoreoProjectsComponentProps {
  orgId?: number;
  orgHandler?: string;
  organizationName?: string;
}

const columns: TableColumn[] = [
  { title: 'ID', field: 'id' },
  { title: 'Name', field: 'name' },
  { title: 'Description', field: 'description' },
  { title: 'Version', field: 'version' },
  { title: 'Region', field: 'region' },
  { title: 'Handler', field: 'handler' },
];

export const ChoreoProjectsComponent = ({
  orgId,
  orgHandler,
  organizationName
}: ChoreoProjectsComponentProps) => {
  const choreoApi = useApi(choreoApiRef);
  const navigate = useNavigate();
  const componentsRoute = useRouteRef(componentsRouteRef);

  const { value, loading, error } = useAsync(async () => {
    if (!orgId || !orgHandler) {
      return [];
    }
    const response = await choreoApi.getProjects(orgId, orgHandler);
    return response.projects;
  }, [choreoApi, orgId, orgHandler]);

  if (loading) {
    return <Progress />;
  }

  if (error) {
    return <ResponseErrorPanel error={error} />;
  }

  const projects = value || [];

  const handleRowClick = (event: any, rowData: any) => {
    if (orgHandler && rowData.id) {
      const componentsPath = componentsRoute({
        orgHandler,
        projectId: rowData.id,
      });
      navigate(componentsPath);
    }
  };

  const data = projects.map((project: ChoreoProject) => ({
    id: project.id,
    name: project.name,
    description: project.description || 'No description',
    version: project.version,
    region: project.region,
    handler: project.handler,
  }));

  const title = organizationName
    ? `Projects in ${organizationName}`
    : 'Choreo Projects';

  return (
    <InfoCard title={title}>
      <Table
        title="Projects"
        options={{
          search: true,
          paging: false,
        }}
        columns={columns}
        data={data}
        onRowClick={handleRowClick}
        emptyContent={
          orgId
            ? 'No projects found in this organization'
            : 'No projects found'
        }
      />
    </InfoCard>
  );
};
