import React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>ield,
  Typo<PERSON>,
  FormHelperText,
  Tooltip,
  IconButton,
} from '@material-ui/core';
import { Help as HelpIcon } from '@material-ui/icons';
import { WebAppCreationData } from '../../api';
import { validateRepositoryUrl, validateBranch, ValidationError } from './validation';

interface Step1RepositoryConfigProps {
  data: Partial<WebAppCreationData>;
  onChange: (field: keyof WebAppCreationData, value: string) => void;
  errors?: ValidationError[];
}

export const Step1RepositoryConfig: React.FC<Step1RepositoryConfigProps> = ({
  data,
  onChange,
  errors,
}) => {
  const getFieldError = (field: string): string | undefined => {
    return errors?.find(error => error.field === field)?.message;
  };

  const handleRepositoryUrlChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    onChange('repositoryUrl', value);
    
    // Auto-suggest branch if URL is valid
    if (value && !data.branch && validateRepositoryUrl(value) === null) {
      // Default to 'main' for new repos, but could be 'master' for older ones
      onChange('branch', 'main');
    }
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Repository Configuration
      </Typography>
      <Typography variant="body2" color="textSecondary" paragraph>
        Configure the source repository for your web application
      </Typography>

      <Box mb={3}>
        <Box display="flex" alignItems="center" mb={1}>
          <Typography variant="subtitle1" component="label">
            GitHub Repository URL *
          </Typography>
          <Tooltip title="Enter the public GitHub repository URL where your web application source code is located. Example: https://github.com/username/my-react-app">
            <IconButton size="small" style={{ marginLeft: 4 }}>
              <HelpIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
        <TextField
          fullWidth
          variant="outlined"
          placeholder="https://github.com/username/repository-name"
          value={data.repositoryUrl || ''}
          onChange={handleRepositoryUrlChange}
          error={!!getFieldError('repositoryUrl')}
          helperText={getFieldError('repositoryUrl')}
        />
        <FormHelperText>
          Only public GitHub repositories are supported
        </FormHelperText>
      </Box>

      <Box mb={3}>
        <Box display="flex" alignItems="center" mb={1}>
          <Typography variant="subtitle1" component="label">
            Branch *
          </Typography>
          <Tooltip title="The Git branch to use for building your application. Common branches are 'main' or 'master'">
            <IconButton size="small" style={{ marginLeft: 4 }}>
              <HelpIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
        <TextField
          fullWidth
          variant="outlined"
          placeholder="main"
          value={data.branch || ''}
          onChange={(e) => onChange('branch', e.target.value)}
          error={!!getFieldError('branch')}
          helperText={getFieldError('branch')}
        />
      </Box>

      <Box mb={3}>
        <Box display="flex" alignItems="center" mb={1}>
          <Typography variant="subtitle1" component="label">
            Component Directory
          </Typography>
          <Tooltip title="Optional: Specify a subdirectory within the repository where your application code is located. Leave empty if your code is in the root directory">
            <IconButton size="small" style={{ marginLeft: 4 }}>
              <HelpIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
        <TextField
          fullWidth
          variant="outlined"
          placeholder="frontend (optional)"
          value={data.componentDirectory || ''}
          onChange={(e) => onChange('componentDirectory', e.target.value)}
        />
        <FormHelperText>
          Leave empty if your application is in the root directory
        </FormHelperText>
      </Box>
    </Box>
  );
};
