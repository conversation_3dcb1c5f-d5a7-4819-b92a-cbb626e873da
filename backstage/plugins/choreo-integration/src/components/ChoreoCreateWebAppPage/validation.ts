import { WebAppCreationData } from '../../api';

export interface ValidationError {
  field: string;
  message: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

// GitHub URL validation regex
const GITHUB_URL_REGEX = /^https:\/\/github\.com\/[a-zA-Z0-9_.-]+\/[a-zA-Z0-9_.-]+(?:\.git)?$/;

// Component name validation regex (alphanumeric, hyphens, underscores)
const COMPONENT_NAME_REGEX = /^[a-zA-Z0-9][a-zA-Z0-9_-]*[a-zA-Z0-9]$|^[a-zA-Z0-9]$/;

export const validateRepositoryUrl = (url: string): ValidationError | null => {
  if (!url.trim()) {
    return { field: 'repositoryUrl', message: 'Repository URL is required' };
  }
  
  if (!GITHUB_URL_REGEX.test(url.trim())) {
    return { 
      field: 'repositoryUrl', 
      message: 'Please enter a valid public GitHub repository URL (e.g., https://github.com/user/repo)' 
    };
  }
  
  return null;
};

export const validateBranch = (branch: string): ValidationError | null => {
  if (!branch.trim()) {
    return { field: 'branch', message: 'Branch name is required' };
  }
  
  // Basic branch name validation
  if (branch.includes(' ') || branch.includes('..')) {
    return { field: 'branch', message: 'Invalid branch name format' };
  }
  
  return null;
};

export const validateDisplayName = (displayName: string): ValidationError | null => {
  if (!displayName.trim()) {
    return { field: 'displayName', message: 'Display name is required' };
  }
  
  if (displayName.trim().length < 2) {
    return { field: 'displayName', message: 'Display name must be at least 2 characters long' };
  }
  
  if (displayName.trim().length > 50) {
    return { field: 'displayName', message: 'Display name must be less than 50 characters' };
  }
  
  return null;
};

export const validateComponentName = (componentName: string): ValidationError | null => {
  if (!componentName.trim()) {
    return { field: 'componentName', message: 'Component name is required' };
  }
  
  if (!COMPONENT_NAME_REGEX.test(componentName.trim())) {
    return { 
      field: 'componentName', 
      message: 'Component name must contain only alphanumeric characters, hyphens, and underscores' 
    };
  }
  
  if (componentName.trim().length < 2) {
    return { field: 'componentName', message: 'Component name must be at least 2 characters long' };
  }
  
  if (componentName.trim().length > 30) {
    return { field: 'componentName', message: 'Component name must be less than 30 characters' };
  }
  
  return null;
};

export const validateDescription = (description: string): ValidationError | null => {
  if (description && description.length > 200) {
    return { field: 'description', message: 'Description must be less than 200 characters' };
  }
  
  return null;
};

export const validateBuildCommand = (buildCommand: string, required: boolean = false): ValidationError | null => {
  if (required && !buildCommand.trim()) {
    return { field: 'buildCommand', message: 'Build command is required for this preset' };
  }
  
  return null;
};

export const validateBuildPath = (buildPath: string, required: boolean = false): ValidationError | null => {
  if (required && !buildPath.trim()) {
    return { field: 'buildPath', message: 'Build path is required for this preset' };
  }
  
  return null;
};

export const validatePort = (port: number | undefined, required: boolean = false): ValidationError | null => {
  if (required && (port === undefined || port === null)) {
    return { field: 'port', message: 'Port is required for this preset' };
  }
  
  if (port !== undefined && (port < 1 || port > 65535)) {
    return { field: 'port', message: 'Port must be between 1 and 65535' };
  }
  
  return null;
};

export const generateComponentName = (displayName: string): string => {
  return displayName
    .toLowerCase()
    .replace(/[^a-zA-Z0-9\s-]/g, '') // Remove special characters except spaces and hyphens
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
};

export const validateStep1 = (data: Partial<WebAppCreationData>): ValidationResult => {
  const errors: ValidationError[] = [];
  
  const urlError = validateRepositoryUrl(data.repositoryUrl || '');
  if (urlError) errors.push(urlError);
  
  const branchError = validateBranch(data.branch || '');
  if (branchError) errors.push(branchError);
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

export const validateStep2 = (data: Partial<WebAppCreationData>): ValidationResult => {
  const errors: ValidationError[] = [];
  
  const displayNameError = validateDisplayName(data.displayName || '');
  if (displayNameError) errors.push(displayNameError);
  
  const componentNameError = validateComponentName(data.componentName || '');
  if (componentNameError) errors.push(componentNameError);
  
  const descriptionError = validateDescription(data.description || '');
  if (descriptionError) errors.push(descriptionError);
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

export const validateStep3 = (data: Partial<WebAppCreationData>): ValidationResult => {
  const errors: ValidationError[] = [];
  
  if (!data.buildPreset) {
    errors.push({ field: 'buildPreset', message: 'Build preset is required' });
    return { isValid: false, errors };
  }
  
  // Validate based on preset requirements
  const preset = data.buildPreset;
  const config = data.buildConfig || {};
  
  if (['react', 'angular'].includes(preset)) {
    const buildCommandError = validateBuildCommand(config.buildCommand || '', true);
    if (buildCommandError) errors.push(buildCommandError);
    
    const buildPathError = validateBuildPath(config.buildPath || '', true);
    if (buildPathError) errors.push(buildPathError);
  }
  
  if (['nodejs', 'dotnet', 'python', 'spring-boot', 'php', 'go', 'ruby', 'docker'].includes(preset)) {
    const portError = validatePort(config.port, true);
    if (portError) errors.push(portError);
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

export const validateAllSteps = (data: WebAppCreationData): ValidationResult => {
  const step1Result = validateStep1(data);
  const step2Result = validateStep2(data);
  const step3Result = validateStep3(data);
  
  const allErrors = [...step1Result.errors, ...step2Result.errors, ...step3Result.errors];
  
  return {
    isValid: allErrors.length === 0,
    errors: allErrors,
  };
};
