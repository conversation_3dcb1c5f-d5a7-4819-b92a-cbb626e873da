import React from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
} from '@material-ui/core';
import {
  GitHub as GitHubIcon,
  Settings as SettingsIcon,
  Info as InfoIcon,
  Build as BuildIcon,
} from '@material-ui/icons';
import { WebAppCreationData } from '../../api';
import { getPresetById } from './buildPresets';

interface Step4ReviewDeployProps {
  data: WebAppCreationData;
  orgHandler: string;
  projectId: string;
}

export const Step4ReviewDeploy: React.FC<Step4ReviewDeployProps> = ({
  data,
  orgHandler,
  projectId,
}) => {
  const selectedPreset = getPresetById(data.buildPreset);
  const buildConfig = data.buildConfig || {};

  const renderBuildConfigSummary = () => {
    if (!selectedPreset) return null;

    const configItems = [];

    // Add preset-specific configuration items
    if (['react', 'angular'].includes(selectedPreset.id)) {
      if (buildConfig.buildCommand) {
        configItems.push({ label: 'Build Command', value: buildConfig.buildCommand });
      }
      if (buildConfig.buildPath) {
        configItems.push({ label: 'Build Output Directory', value: buildConfig.buildPath });
      }
      if (buildConfig.nodeVersion) {
        configItems.push({ label: 'Node.js Version', value: buildConfig.nodeVersion });
      }
    }

    if (['nodejs', 'dotnet', 'python', 'spring-boot', 'php', 'go', 'ruby', 'docker'].includes(selectedPreset.id)) {
      if (buildConfig.languageVersion && selectedPreset.id !== 'docker') {
        configItems.push({ label: 'Language Version', value: buildConfig.languageVersion });
      }
      if (buildConfig.port) {
        configItems.push({ label: 'Port', value: buildConfig.port.toString() });
      }
    }

    if (selectedPreset.id === 'python' && buildConfig.runCommand) {
      configItems.push({ label: 'Run Command', value: buildConfig.runCommand });
    }

    if (selectedPreset.id === 'docker' && buildConfig.dockerfilePath) {
      configItems.push({ label: 'Dockerfile Path', value: buildConfig.dockerfilePath });
    }

    return configItems;
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Review & Deploy
      </Typography>
      <Typography variant="body2" color="textSecondary" paragraph>
        Review your configuration and create the web application component
      </Typography>

      <Grid container spacing={3}>
        {/* Repository Configuration */}
        <Grid item xs={12} md={6}>
          <Card variant="outlined">
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <GitHubIcon color="primary" style={{ marginRight: 8 }} />
                <Typography variant="h6">Repository</Typography>
              </Box>
              <List dense>
                <ListItem>
                  <ListItemText
                    primary="Repository URL"
                    secondary={data.repositoryUrl}
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Branch"
                    secondary={data.branch}
                  />
                </ListItem>
                {data.componentDirectory && (
                  <ListItem>
                    <ListItemText
                      primary="Component Directory"
                      secondary={data.componentDirectory}
                    />
                  </ListItem>
                )}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Component Details */}
        <Grid item xs={12} md={6}>
          <Card variant="outlined">
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <InfoIcon color="primary" style={{ marginRight: 8 }} />
                <Typography variant="h6">Component Details</Typography>
              </Box>
              <List dense>
                <ListItem>
                  <ListItemText
                    primary="Display Name"
                    secondary={data.displayName}
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Component Name"
                    secondary={data.componentName}
                  />
                </ListItem>
                {data.description && (
                  <ListItem>
                    <ListItemText
                      primary="Description"
                      secondary={data.description}
                    />
                  </ListItem>
                )}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Build Configuration */}
        <Grid item xs={12}>
          <Card variant="outlined">
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <BuildIcon color="primary" style={{ marginRight: 8 }} />
                <Typography variant="h6">Build Configuration</Typography>
              </Box>
              
              <Box mb={2}>
                <Typography variant="subtitle2" gutterBottom>
                  Build Preset
                </Typography>
                <Chip 
                  label={selectedPreset?.name || 'Unknown'} 
                  color="primary" 
                  variant="outlined"
                />
                {selectedPreset && (
                  <Typography variant="body2" color="textSecondary" style={{ marginTop: 4 }}>
                    {selectedPreset.description}
                  </Typography>
                )}
              </Box>

              {renderBuildConfigSummary()?.length > 0 && (
                <>
                  <Divider style={{ margin: '16px 0' }} />
                  <Typography variant="subtitle2" gutterBottom>
                    Configuration Details
                  </Typography>
                  <List dense>
                    {renderBuildConfigSummary()?.map((item, index) => (
                      <ListItem key={index}>
                        <ListItemText
                          primary={item.label}
                          secondary={item.value}
                        />
                      </ListItem>
                    ))}
                  </List>
                </>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Project Context */}
        <Grid item xs={12}>
          <Card variant="outlined">
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <SettingsIcon color="primary" style={{ marginRight: 8 }} />
                <Typography variant="h6">Deployment Context</Typography>
              </Box>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">Organization</Typography>
                  <Typography variant="body2" color="textSecondary">
                    {orgHandler}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">Project</Typography>
                  <Typography variant="body2" color="textSecondary">
                    {projectId}
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Box
        mt={3}
        p={2}
        style={{
          backgroundColor: '#1565C0', // Darker blue for better contrast
          color: '#ffffff',
          borderRadius: 4,
          border: '1px solid #0d47a1'
        }}
      >
        <Typography variant="body2" style={{ color: '#ffffff', lineHeight: 1.6 }}>
          <strong style={{ color: '#ffffff' }}>What happens next?</strong>
          <br />
          1. Your component will be created in the Choreo Console
          <br />
          2. The initial build will be triggered automatically
          <br />
          3. Once built, your web application will be deployed to the development environment
          <br />
          4. You can monitor the progress in the Choreo Console
        </Typography>
      </Box>
    </Box>
  );
};
