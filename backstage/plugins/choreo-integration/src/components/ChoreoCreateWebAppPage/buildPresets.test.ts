import { BUILD_PRESETS, getPresetById, getDefaultPreset } from './buildPresets';

describe('buildPresets', () => {
  describe('BUILD_PRESETS', () => {
    it('should contain all expected presets', () => {
      const expectedPresets = [
        'react',
        'nodejs',
        'angular',
        'dotnet',
        'python',
        'spring-boot',
        'php',
        'go',
        'ruby',
        'static',
        'docker',
      ];

      const presetIds = BUILD_PRESETS.map(preset => preset.id);
      expect(presetIds).toEqual(expectedPresets);
    });

    it('should have valid structure for all presets', () => {
      BUILD_PRESETS.forEach(preset => {
        expect(preset).toHaveProperty('id');
        expect(preset).toHaveProperty('name');
        expect(preset).toHaveProperty('description');
        expect(preset).toHaveProperty('defaultConfig');
        
        expect(typeof preset.id).toBe('string');
        expect(typeof preset.name).toBe('string');
        expect(typeof preset.description).toBe('string');
        expect(typeof preset.defaultConfig).toBe('object');
      });
    });

    it('should have React preset with correct default config', () => {
      const reactPreset = BUILD_PRESETS.find(p => p.id === 'react');
      
      expect(reactPreset).toBeDefined();
      expect(reactPreset?.defaultConfig).toEqual({
        buildCommand: 'npm run build',
        buildPath: 'build',
        nodeVersion: '18',
      });
    });

    it('should have NodeJS preset with correct default config', () => {
      const nodejsPreset = BUILD_PRESETS.find(p => p.id === 'nodejs');
      
      expect(nodejsPreset).toBeDefined();
      expect(nodejsPreset?.defaultConfig).toEqual({
        languageVersion: '18',
        port: 3000,
      });
    });

    it('should have Docker preset with correct default config', () => {
      const dockerPreset = BUILD_PRESETS.find(p => p.id === 'docker');
      
      expect(dockerPreset).toBeDefined();
      expect(dockerPreset?.defaultConfig).toEqual({
        dockerfilePath: 'Dockerfile',
        port: 8080,
      });
    });
  });

  describe('getPresetById', () => {
    it('should return preset for valid ID', () => {
      const preset = getPresetById('react');
      
      expect(preset).toBeDefined();
      expect(preset?.id).toBe('react');
      expect(preset?.name).toBe('React');
    });

    it('should return undefined for invalid ID', () => {
      const preset = getPresetById('invalid-preset');
      
      expect(preset).toBeUndefined();
    });

    it('should return correct preset for all valid IDs', () => {
      const validIds = [
        'react',
        'nodejs',
        'angular',
        'dotnet',
        'python',
        'spring-boot',
        'php',
        'go',
        'ruby',
        'static',
        'docker',
      ];

      validIds.forEach(id => {
        const preset = getPresetById(id);
        expect(preset).toBeDefined();
        expect(preset?.id).toBe(id);
      });
    });
  });

  describe('getDefaultPreset', () => {
    it('should return React as default preset', () => {
      const defaultPreset = getDefaultPreset();
      
      expect(defaultPreset.id).toBe('react');
      expect(defaultPreset.name).toBe('React');
    });

    it('should return the first preset in the array', () => {
      const defaultPreset = getDefaultPreset();
      const firstPreset = BUILD_PRESETS[0];
      
      expect(defaultPreset).toEqual(firstPreset);
    });
  });

  describe('preset configurations', () => {
    it('should have build commands for frontend presets', () => {
      const frontendPresets = ['react', 'angular'];
      
      frontendPresets.forEach(presetId => {
        const preset = getPresetById(presetId);
        expect(preset?.defaultConfig.buildCommand).toBeDefined();
        expect(preset?.defaultConfig.buildPath).toBeDefined();
        expect(preset?.defaultConfig.nodeVersion).toBeDefined();
      });
    });

    it('should have ports for server presets', () => {
      const serverPresets = ['nodejs', 'dotnet', 'python', 'spring-boot', 'php', 'go', 'ruby', 'docker'];
      
      serverPresets.forEach(presetId => {
        const preset = getPresetById(presetId);
        expect(preset?.defaultConfig.port).toBeDefined();
        expect(typeof preset?.defaultConfig.port).toBe('number');
      });
    });

    it('should have language versions for runtime presets', () => {
      const runtimePresets = ['nodejs', 'dotnet', 'python', 'spring-boot', 'php', 'go', 'ruby'];
      
      runtimePresets.forEach(presetId => {
        const preset = getPresetById(presetId);
        expect(preset?.defaultConfig.languageVersion).toBeDefined();
        expect(typeof preset?.defaultConfig.languageVersion).toBe('string');
      });
    });

    it('should have dockerfile path for Docker preset', () => {
      const dockerPreset = getPresetById('docker');
      
      expect(dockerPreset?.defaultConfig.dockerfilePath).toBeDefined();
      expect(dockerPreset?.defaultConfig.dockerfilePath).toBe('Dockerfile');
    });

    it('should have run command for Python preset', () => {
      const pythonPreset = getPresetById('python');
      
      expect(pythonPreset?.defaultConfig.runCommand).toBeDefined();
      expect(pythonPreset?.defaultConfig.runCommand).toBe('python app.py');
    });
  });
});
