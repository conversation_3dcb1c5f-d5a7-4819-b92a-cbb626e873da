import { WebAppBuildPreset } from '../../api';

export const BUILD_PRESETS: WebAppBuildPreset[] = [
  {
    id: 'react',
    name: 'React',
    description: 'React application with npm/yarn build process',
    defaultConfig: {
      buildCommand: 'npm run build',
      buildPath: 'build',
      nodeVersion: '18',
    },
  },
  {
    id: 'nodejs',
    name: 'NodeJS',
    description: 'Node.js server application',
    defaultConfig: {
      languageVersion: '18',
      port: 3000,
    },
  },
  {
    id: 'angular',
    name: 'Angular',
    description: 'Angular application with Angular CLI',
    defaultConfig: {
      buildCommand: 'ng build',
      buildPath: 'dist',
      nodeVersion: '18',
    },
  },
  {
    id: 'dotnet',
    name: '.NET',
    description: '.NET Core web application',
    defaultConfig: {
      languageVersion: '6.0',
      port: 5000,
    },
  },
  {
    id: 'python',
    name: 'Python',
    description: 'Python web application (Flask/Django)',
    defaultConfig: {
      languageVersion: '3.9',
      runCommand: 'python app.py',
      port: 8000,
    },
  },
  {
    id: 'spring-boot',
    name: 'Spring Boot',
    description: 'Java Spring Boot application',
    defaultConfig: {
      languageVersion: '11',
      port: 8080,
    },
  },
  {
    id: 'php',
    name: 'PHP',
    description: 'PHP web application',
    defaultConfig: {
      languageVersion: '8.1',
      port: 80,
    },
  },
  {
    id: 'go',
    name: 'Go',
    description: 'Go web application',
    defaultConfig: {
      languageVersion: '1.19',
      port: 8080,
    },
  },
  {
    id: 'ruby',
    name: 'Ruby',
    description: 'Ruby web application (Rails/Sinatra)',
    defaultConfig: {
      languageVersion: '3.0',
      port: 3000,
    },
  },
  {
    id: 'static',
    name: 'Static Website',
    description: 'Static HTML/CSS/JS website',
    defaultConfig: {
      buildPath: '.',
    },
  },
  {
    id: 'docker',
    name: 'Docker',
    description: 'Custom Docker container',
    defaultConfig: {
      dockerfilePath: 'Dockerfile',
      port: 8080,
    },
  },
];

export const getPresetById = (id: string): WebAppBuildPreset | undefined => {
  return BUILD_PRESETS.find(preset => preset.id === id);
};

export const getDefaultPreset = (): WebAppBuildPreset => {
  return BUILD_PRESETS[0]; // React as default
};
