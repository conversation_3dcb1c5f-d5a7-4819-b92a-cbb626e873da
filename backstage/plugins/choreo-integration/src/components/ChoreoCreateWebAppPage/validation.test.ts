import {
  validateRepositoryUrl,
  validateBranch,
  validateDisplayName,
  validateComponentName,
  validateDescription,
  validateBuildCommand,
  validateBuildPath,
  validatePort,
  generateComponentName,
  validateStep1,
  validateStep2,
  validateStep3,
  validateAllSteps,
} from './validation';

describe('validation utilities', () => {
  describe('validateRepositoryUrl', () => {
    it('should return error for empty URL', () => {
      const result = validateRepositoryUrl('');
      expect(result).toEqual({
        field: 'repositoryUrl',
        message: 'Repository URL is required',
      });
    });

    it('should return error for invalid GitHub URL', () => {
      const result = validateRepositoryUrl('https://gitlab.com/user/repo');
      expect(result).toEqual({
        field: 'repositoryUrl',
        message: 'Please enter a valid public GitHub repository URL (e.g., https://github.com/user/repo)',
      });
    });

    it('should return null for valid GitHub URL', () => {
      const result = validateRepositoryUrl('https://github.com/user/repo');
      expect(result).toBeNull();
    });

    it('should return null for valid GitHub URL with .git extension', () => {
      const result = validateRepositoryUrl('https://github.com/user/repo.git');
      expect(result).toBeNull();
    });
  });

  describe('validateBranch', () => {
    it('should return error for empty branch', () => {
      const result = validateBranch('');
      expect(result).toEqual({
        field: 'branch',
        message: 'Branch name is required',
      });
    });

    it('should return error for branch with spaces', () => {
      const result = validateBranch('main branch');
      expect(result).toEqual({
        field: 'branch',
        message: 'Invalid branch name format',
      });
    });

    it('should return null for valid branch name', () => {
      const result = validateBranch('main');
      expect(result).toBeNull();
    });
  });

  describe('validateDisplayName', () => {
    it('should return error for empty display name', () => {
      const result = validateDisplayName('');
      expect(result).toEqual({
        field: 'displayName',
        message: 'Display name is required',
      });
    });

    it('should return error for display name too short', () => {
      const result = validateDisplayName('A');
      expect(result).toEqual({
        field: 'displayName',
        message: 'Display name must be at least 2 characters long',
      });
    });

    it('should return error for display name too long', () => {
      const result = validateDisplayName('A'.repeat(51));
      expect(result).toEqual({
        field: 'displayName',
        message: 'Display name must be less than 50 characters',
      });
    });

    it('should return null for valid display name', () => {
      const result = validateDisplayName('My App');
      expect(result).toBeNull();
    });
  });

  describe('validateComponentName', () => {
    it('should return error for empty component name', () => {
      const result = validateComponentName('');
      expect(result).toEqual({
        field: 'componentName',
        message: 'Component name is required',
      });
    });

    it('should return error for component name with spaces', () => {
      const result = validateComponentName('my app');
      expect(result).toEqual({
        field: 'componentName',
        message: 'Component name must contain only alphanumeric characters, hyphens, and underscores',
      });
    });

    it('should return null for valid component name', () => {
      const result = validateComponentName('my-app');
      expect(result).toBeNull();
    });

    it('should return null for component name with underscores', () => {
      const result = validateComponentName('my_app');
      expect(result).toBeNull();
    });
  });

  describe('validatePort', () => {
    it('should return error for required port that is undefined', () => {
      const result = validatePort(undefined, true);
      expect(result).toEqual({
        field: 'port',
        message: 'Port is required for this preset',
      });
    });

    it('should return error for port out of range', () => {
      const result = validatePort(70000);
      expect(result).toEqual({
        field: 'port',
        message: 'Port must be between 1 and 65535',
      });
    });

    it('should return null for valid port', () => {
      const result = validatePort(3000);
      expect(result).toBeNull();
    });
  });

  describe('generateComponentName', () => {
    it('should generate component name from display name', () => {
      const result = generateComponentName('My React App');
      expect(result).toBe('my-react-app');
    });

    it('should handle special characters', () => {
      const result = generateComponentName('My App! @#$');
      expect(result).toBe('my-app');
    });

    it('should handle multiple spaces', () => {
      const result = generateComponentName('My   App');
      expect(result).toBe('my-app');
    });
  });

  describe('validateStep1', () => {
    it('should return errors for invalid step 1 data', () => {
      const result = validateStep1({
        repositoryUrl: '',
        branch: '',
      });
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(2);
      expect(result.errors[0].field).toBe('repositoryUrl');
      expect(result.errors[1].field).toBe('branch');
    });

    it('should return valid for correct step 1 data', () => {
      const result = validateStep1({
        repositoryUrl: 'https://github.com/user/repo',
        branch: 'main',
      });
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });
  });

  describe('validateStep2', () => {
    it('should return errors for invalid step 2 data', () => {
      const result = validateStep2({
        displayName: '',
        componentName: '',
      });
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(2);
    });

    it('should return valid for correct step 2 data', () => {
      const result = validateStep2({
        displayName: 'My App',
        componentName: 'my-app',
        description: 'A test app',
      });
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });
  });

  describe('validateStep3', () => {
    it('should return error for missing build preset', () => {
      const result = validateStep3({});
      
      expect(result.isValid).toBe(false);
      expect(result.errors[0].field).toBe('buildPreset');
    });

    it('should validate React preset requirements', () => {
      const result = validateStep3({
        buildPreset: 'react',
        buildConfig: {
          buildCommand: '',
          buildPath: '',
        },
      });
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(2);
    });

    it('should validate NodeJS preset requirements', () => {
      const result = validateStep3({
        buildPreset: 'nodejs',
        buildConfig: {},
      });
      
      expect(result.isValid).toBe(false);
      expect(result.errors[0].field).toBe('port');
    });

    it('should return valid for correct React configuration', () => {
      const result = validateStep3({
        buildPreset: 'react',
        buildConfig: {
          buildCommand: 'npm run build',
          buildPath: 'build',
          nodeVersion: '18',
        },
      });
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });
  });
});
