import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { TestApiProvider } from '@backstage/test-utils';
import { ChoreoCreateComponentPage } from './ChoreoCreateComponentPage';
import { componentsRouteRef, createMicroserviceRouteRef, createWebAppRouteRef } from '../../routes';

// Mock the useRouteRef hook
const mockNavigate = jest.fn();
const mockUseRouteRef = jest.fn();

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  useParams: () => ({
    orgHandler: 'test-org',
    projectId: 'test-project-123',
  }),
}));

jest.mock('@backstage/core-plugin-api', () => ({
  ...jest.requireActual('@backstage/core-plugin-api'),
  useRouteRef: (routeRef: any) => mockUseRouteRef(routeRef),
}));

describe('ChoreoCreateComponentPage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseRouteRef.mockImplementation((routeRef) => {
      if (routeRef === componentsRouteRef) {
        return ({ orgHandler, projectId }: any) => `/components/${orgHandler}/${projectId}`;
      }
      if (routeRef === createMicroserviceRouteRef) {
        return ({ orgHandler, projectId }: any) => `/microservices/${orgHandler}/${projectId}`;
      }
      if (routeRef === createWebAppRouteRef) {
        return ({ orgHandler, projectId }: any) => `/webApp/${orgHandler}/${projectId}`;
      }
      return () => '/';
    });
  });

  const renderComponent = () => {
    return render(
      <MemoryRouter>
        <TestApiProvider apis={[]}>
          <ChoreoCreateComponentPage />
        </TestApiProvider>
      </MemoryRouter>
    );
  };

  it('renders the page title and description', () => {
    renderComponent();
    
    expect(screen.getByText('Create a New Component')).toBeInTheDocument();
    expect(screen.getByText('Choose the type of component you want to create in your project')).toBeInTheDocument();
  });

  it('renders all component type cards', () => {
    renderComponent();
    
    expect(screen.getByText('Service')).toBeInTheDocument();
    expect(screen.getByText('Web Application')).toBeInTheDocument();
    expect(screen.getByText('Scheduled Task')).toBeInTheDocument();
    expect(screen.getByText('Manual Task')).toBeInTheDocument();
    expect(screen.getByText('Test Runner')).toBeInTheDocument();
    expect(screen.getByText('Webhook')).toBeInTheDocument();
  });

  it('navigates to microservice creation when Service card is clicked', () => {
    renderComponent();

    const serviceCard = screen.getByRole('button', { name: /create service/i });
    expect(serviceCard).toBeInTheDocument();

    fireEvent.click(serviceCard);

    expect(mockNavigate).toHaveBeenCalledWith('/microservices/test-org/test-project-123');
  });

  it('navigates to web app creation when Web Application card is clicked', () => {
    renderComponent();

    const webAppCard = screen.getByRole('button', { name: /create web application/i });
    expect(webAppCard).toBeInTheDocument();

    fireEvent.click(webAppCard);

    expect(mockNavigate).toHaveBeenCalledWith('/webApp/test-org/test-project-123');
  });

  it('shows tooltip for non-clickable cards', () => {
    renderComponent();
    
    const scheduledTaskCard = screen.getByText('Scheduled Task').closest('.MuiCard-root');
    expect(scheduledTaskCard).toBeInTheDocument();
    
    // Check that the card has reduced opacity (non-clickable styling)
    expect(scheduledTaskCard).toHaveStyle('opacity: 0.6');
  });

  it('navigates back to components when back button is clicked', () => {
    renderComponent();

    const backButton = screen.getByRole('button', { name: /back/i });
    fireEvent.click(backButton);

    expect(mockNavigate).toHaveBeenCalledWith('/components/test-org/test-project-123');
  });

  it('renders component descriptions correctly', () => {
    renderComponent();

    expect(screen.getByText('Create a microservice or API')).toBeInTheDocument();
    expect(screen.getByText('Create a web application')).toBeInTheDocument();
    expect(screen.getByText('Create a scheduled task or cron job')).toBeInTheDocument();
    expect(screen.getByText('Create a manual task')).toBeInTheDocument();
    expect(screen.getByText('Create a test runner')).toBeInTheDocument();
    expect(screen.getByText('Create a webhook handler')).toBeInTheDocument();
    expect(screen.getByText('Create an MCP server')).toBeInTheDocument();
    expect(screen.getByText('Create an API proxy')).toBeInTheDocument();
    expect(screen.getByText('Create an event handler')).toBeInTheDocument();
    expect(screen.getByText('Create an external consumer')).toBeInTheDocument();
  });

  it('displays all component type cards including new ones', () => {
    renderComponent();

    // Original cards
    expect(screen.getByText('Service')).toBeInTheDocument();
    expect(screen.getByText('Web Application')).toBeInTheDocument();
    expect(screen.getByText('Scheduled Task')).toBeInTheDocument();
    expect(screen.getByText('Manual Task')).toBeInTheDocument();
    expect(screen.getByText('Test Runner')).toBeInTheDocument();
    expect(screen.getByText('Webhook')).toBeInTheDocument();

    // New cards
    expect(screen.getByText('MCP Server')).toBeInTheDocument();
    expect(screen.getByText('API Proxy')).toBeInTheDocument();
    expect(screen.getByText('Event Handler')).toBeInTheDocument();
    expect(screen.getByText('External Consumer')).toBeInTheDocument();
  });

  it('ensures new cards are non-clickable with proper styling', () => {
    renderComponent();

    const mcpServerCard = screen.getByText('MCP Server').closest('.MuiCard-root');
    const apiProxyCard = screen.getByText('API Proxy').closest('.MuiCard-root');
    const eventHandlerCard = screen.getByText('Event Handler').closest('.MuiCard-root');
    const externalConsumerCard = screen.getByText('External Consumer').closest('.MuiCard-root');

    expect(mcpServerCard).toHaveStyle('opacity: 0.6');
    expect(apiProxyCard).toHaveStyle('opacity: 0.6');
    expect(eventHandlerCard).toHaveStyle('opacity: 0.6');
    expect(externalConsumerCard).toHaveStyle('opacity: 0.6');
  });
});
