import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { TestApiProvider } from '@backstage/test-utils';
import { ChoreoCreateMicroservicePage } from './ChoreoCreateMicroservicePage';
import { createComponentRouteRef } from '../../routes';

// Mock the useRouteRef hook
const mockNavigate = jest.fn();
const mockUseRouteRef = jest.fn();

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  useParams: () => ({
    orgHandler: 'test-org',
    projectId: 'test-project-123',
  }),
}));

jest.mock('@backstage/core-plugin-api', () => ({
  ...jest.requireActual('@backstage/core-plugin-api'),
  useRouteRef: (routeRef: any) => mockUseRouteRef(routeRef),
}));

describe('ChoreoCreateMicroservicePage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseRouteRef.mockImplementation((routeRef) => {
      if (routeRef === createComponentRouteRef) {
        return ({ orgHandler, projectId }: any) => `/create/${orgHandler}/${projectId}`;
      }
      return () => '/';
    });
  });

  const renderComponent = () => {
    return render(
      <MemoryRouter>
        <TestApiProvider apis={[]}>
          <ChoreoCreateMicroservicePage />
        </TestApiProvider>
      </MemoryRouter>
    );
  };

  it('renders the page title and description', () => {
    renderComponent();
    
    expect(screen.getByText('Create a Microservice')).toBeInTheDocument();
    expect(screen.getByText('Configure your new microservice component')).toBeInTheDocument();
  });

  it('displays project and organization information', () => {
    renderComponent();

    expect(screen.getByText(/test-project-123/)).toBeInTheDocument();
    expect(screen.getByText(/test-org/)).toBeInTheDocument();
  });

  it('shows placeholder content for the form', () => {
    renderComponent();
    
    expect(screen.getByText('Microservice Creation Form')).toBeInTheDocument();
    expect(screen.getByText(/This page will contain the microservice creation form/)).toBeInTheDocument();
  });

  it('navigates back to create component page when back button is clicked', () => {
    renderComponent();
    
    const backButton = screen.getByRole('button', { name: /back/i });
    fireEvent.click(backButton);
    
    expect(mockNavigate).toHaveBeenCalledWith('/create/test-org/test-project-123');
  });
});
