import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Typography,
  Box,
  IconButton,
  Paper,
} from '@material-ui/core';
import { ArrowBack as ArrowBackIcon } from '@material-ui/icons';
import { useRouteRef } from '@backstage/core-plugin-api';
import { createComponentRouteRef } from '../../routes';

export const ChoreoCreateMicroservicePage = () => {
  const { orgHandler, projectId } = useParams<{
    orgHandler: string;
    projectId: string;
  }>();
  const navigate = useNavigate();
  const createComponentRoute = useRouteRef(createComponentRouteRef);

  const handleBackToCreate = () => {
    if (orgHandler && projectId) {
      navigate(createComponentRoute({ orgHandler, projectId }));
    }
  };

  return (
    <Box p={3}>
      <Box mb={3} display="flex" alignItems="center">
        <IconButton
          onClick={handleBackToCreate}
          style={{ marginRight: 16 }}
          aria-label="back to create component"
        >
          <ArrowBackIcon />
        </IconButton>
        <Box>
          <Typography variant="h4" gutterBottom>
            Create a Microservice
          </Typography>
          <Typography variant="body1" color="textSecondary">
            Configure your new microservice component
          </Typography>
        </Box>
      </Box>

      <Paper style={{ padding: 24 }}>
        <Typography variant="h6" gutterBottom>
          Microservice Creation Form
        </Typography>
        <Typography variant="body1" color="textSecondary">
          This page will contain the microservice creation form.
          Implementation details will be added based on Choreo API requirements.
        </Typography>
        <Box mt={2}>
          <Typography variant="body2">
            <strong>Project:</strong> {projectId}
          </Typography>
          <Typography variant="body2">
            <strong>Organization:</strong> {orgHandler}
          </Typography>
        </Box>
      </Paper>
    </Box>
  );
};
