import { SERVICE_BUILD_PRESETS, getServicePresetById, getDefaultServicePreset } from './serviceBuildPresets';

describe('serviceBuildPresets', () => {
  describe('SERVICE_BUILD_PRESETS', () => {
    it('should contain all expected presets', () => {
      const expectedPresets = [
        'nodejs',
        'python',
        'java',
        'go',
        'dotnet',
        'ballerina',
        'php',
        'ruby',
        'docker',
        'wso2-mi',
        'prism-mock',
      ];

      const presetIds = SERVICE_BUILD_PRESETS.map(preset => preset.id);
      expect(presetIds).toEqual(expectedPresets);
    });

    it('should have valid structure for all presets', () => {
      SERVICE_BUILD_PRESETS.forEach(preset => {
        expect(preset).toHaveProperty('id');
        expect(preset).toHaveProperty('name');
        expect(preset).toHaveProperty('description');
        expect(preset).toHaveProperty('buildpackId');
        expect(preset).toHaveProperty('defaultConfig');
        
        expect(typeof preset.id).toBe('string');
        expect(typeof preset.name).toBe('string');
        expect(typeof preset.description).toBe('string');
        expect(typeof preset.buildpackId).toBe('string');
        expect(typeof preset.defaultConfig).toBe('object');
      });
    });

    it('should have port configuration for all presets', () => {
      SERVICE_BUILD_PRESETS.forEach(preset => {
        expect(preset.defaultConfig.port).toBeDefined();
        expect(typeof preset.defaultConfig.port).toBe('number');
        expect(preset.defaultConfig.port).toBeGreaterThan(0);
      });
    });

    it('should have language version for non-docker presets', () => {
      const nonDockerPresets = SERVICE_BUILD_PRESETS.filter(
        preset => !['docker', 'wso2-mi', 'prism-mock'].includes(preset.id)
      );
      
      nonDockerPresets.forEach(preset => {
        expect(preset.defaultConfig.languageVersion).toBeDefined();
        expect(typeof preset.defaultConfig.languageVersion).toBe('string');
        expect(preset.defaultConfig.languageVersion.length).toBeGreaterThan(0);
      });
    });
  });

  describe('getServicePresetById', () => {
    it('should return preset for valid ID', () => {
      const preset = getServicePresetById('nodejs');
      
      expect(preset).toBeDefined();
      expect(preset?.id).toBe('nodejs');
      expect(preset?.name).toBe('Node.js');
      expect(preset?.buildpackId).toBe('nodejs');
    });

    it('should return undefined for invalid ID', () => {
      const preset = getServicePresetById('invalid-preset');
      
      expect(preset).toBeUndefined();
    });

    it('should return correct preset for all valid IDs', () => {
      const validIds = [
        'nodejs',
        'python',
        'java',
        'go',
        'dotnet',
        'ballerina',
        'php',
        'ruby',
        'docker',
        'wso2-mi',
        'prism-mock',
      ];

      validIds.forEach(id => {
        const preset = getServicePresetById(id);
        expect(preset).toBeDefined();
        expect(preset?.id).toBe(id);
      });
    });
  });

  describe('getDefaultServicePreset', () => {
    it('should return Node.js as default preset', () => {
      const defaultPreset = getDefaultServicePreset();
      
      expect(defaultPreset).toBeDefined();
      expect(defaultPreset.id).toBe('nodejs');
      expect(defaultPreset.name).toBe('Node.js');
    });

    it('should return a valid preset structure', () => {
      const defaultPreset = getDefaultServicePreset();
      
      expect(defaultPreset).toHaveProperty('id');
      expect(defaultPreset).toHaveProperty('name');
      expect(defaultPreset).toHaveProperty('description');
      expect(defaultPreset).toHaveProperty('buildpackId');
      expect(defaultPreset).toHaveProperty('defaultConfig');
    });
  });

  describe('Node.js preset configuration', () => {
    it('should have correct Node.js configuration', () => {
      const nodejsPreset = getServicePresetById('nodejs');
      
      expect(nodejsPreset?.defaultConfig.languageVersion).toBe('20.x.x');
      expect(nodejsPreset?.defaultConfig.port).toBe(3000);
      expect(nodejsPreset?.defaultConfig.runCommand).toBe('npm start');
    });
  });

  describe('Docker preset configuration', () => {
    it('should have dockerfile path for Docker preset', () => {
      const dockerPreset = getServicePresetById('docker');
      
      expect(dockerPreset?.defaultConfig.dockerfilePath).toBeDefined();
      expect(dockerPreset?.defaultConfig.dockerfilePath).toBe('Dockerfile');
    });
  });
});
