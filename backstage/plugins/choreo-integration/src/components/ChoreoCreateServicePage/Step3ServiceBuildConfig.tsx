import React from 'react';
import {
  Box,
  TextField,
  Typography,
  FormControl,
  RadioGroup,
  FormControlLabel,
  Radio,
  Tooltip,
  IconButton,
  Grid,
  Card,
  CardContent,
} from '@material-ui/core';
import { Help as HelpIcon } from '@material-ui/icons';
import { ServiceCreationData, ServiceBuildConfig } from '../../api';
import { SERVICE_BUILD_PRESETS, getServicePresetById } from './serviceBuildPresets';
import { ValidationError } from '../ChoreoCreateWebAppPage/validation';

interface Step3ServiceBuildConfigProps {
  data: Partial<ServiceCreationData>;
  onChange: (field: keyof ServiceCreationData, value: string) => void;
  onBuildConfigChange: (config: ServiceBuildConfig) => void;
  errors?: ValidationError[];
}

export const Step3ServiceBuildConfig: React.FC<Step3ServiceBuildConfigProps> = ({
  data,
  onChange,
  onBuildConfigChange,
  errors,
}) => {
  const getFieldError = (field: string): string | undefined => {
    return errors?.find(error => error.field === field)?.message;
  };

  const selectedPreset = getServicePresetById(data.buildPreset || '');
  const buildConfig = data.buildConfig || {};

  const handlePresetChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const presetId = event.target.value;
    const preset = getServicePresetById(presetId);
    
    onChange('buildPreset', presetId);
    
    if (preset) {
      // Reset build config to preset defaults
      onBuildConfigChange(preset.defaultConfig);
    }
  };

  const handleConfigChange = (field: keyof ServiceBuildConfig, value: string | number) => {
    onBuildConfigChange({
      ...buildConfig,
      [field]: value,
    });
  };

  const renderPresetCard = (preset: typeof SERVICE_BUILD_PRESETS[0]) => (
    <Card
      key={preset.id}
      style={{
        cursor: 'pointer',
        border: data.buildPreset === preset.id ? '3px solid #1976d2' : '1px solid #e0e0e0',
        boxShadow: data.buildPreset === preset.id ? '0 2px 8px rgba(25, 118, 210, 0.2)' : undefined,
        transition: 'all 0.2s ease-in-out',
      }}
      onClick={() => handlePresetChange({ target: { value: preset.id } } as any)}
    >
      <CardContent style={{ padding: 16 }}>
        <Box display="flex" alignItems="center" mb={1}>
          <Radio
            checked={data.buildPreset === preset.id}
            value={preset.id}
            size="small"
            style={{
              padding: 0,
              marginRight: 8,
            }}
            color="primary"
          />
          <Typography
            variant="subtitle2"
            style={{
              fontWeight: 600,
            }}
          >
            {preset.name}
          </Typography>
        </Box>
        <Typography
          variant="body2"
        >
          {preset.description}
        </Typography>
      </CardContent>
    </Card>
  );

  const renderConfigurationFields = () => {
    if (!selectedPreset) return null;

    const config = buildConfig;
    const preset = selectedPreset;

    return (
      <Box mt={3}>
        <Typography variant="h6" gutterBottom>
          Configuration
        </Typography>
        
        {/* Language Version field for most presets */}
        {preset.id !== 'docker' && preset.id !== 'wso2-mi' && preset.id !== 'prism-mock' && (
          <Box mb={2}>
            <Box display="flex" alignItems="center" mb={1}>
              <Typography variant="subtitle2">Language Version</Typography>
              <Tooltip title={`The ${preset.name} version to use for your service`}>
                <IconButton size="small">
                  <HelpIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
            <TextField
              fullWidth
              variant="outlined"
              size="small"
              value={config.languageVersion || ''}
              onChange={(e) => handleConfigChange('languageVersion', e.target.value)}
              placeholder={preset.defaultConfig.languageVersion}
            />
          </Box>
        )}

        {/* Port field for all presets */}
        <Box mb={2}>
          <Box display="flex" alignItems="center" mb={1}>
            <Typography variant="subtitle2">Port</Typography>
            <Tooltip title="The port your service will listen on">
              <IconButton size="small">
                <HelpIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>
          <TextField
            fullWidth
            variant="outlined"
            size="small"
            type="number"
            value={config.port || ''}
            onChange={(e) => handleConfigChange('port', parseInt(e.target.value) || 0)}
            placeholder={preset.defaultConfig.port?.toString()}
          />
        </Box>

        {/* Run Command field for applicable presets */}
        {['nodejs', 'python'].includes(preset.id) && (
          <Box mb={2}>
            <Box display="flex" alignItems="center" mb={1}>
              <Typography variant="subtitle2">Run Command</Typography>
              <Tooltip title="The command to start your service">
                <IconButton size="small">
                  <HelpIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
            <TextField
              fullWidth
              variant="outlined"
              size="small"
              value={config.runCommand || ''}
              onChange={(e) => handleConfigChange('runCommand', e.target.value)}
              placeholder={preset.defaultConfig.runCommand}
            />
          </Box>
        )}

        {/* Docker specific fields */}
        {preset.id === 'docker' && (
          <Box mb={2}>
            <Box display="flex" alignItems="center" mb={1}>
              <Typography variant="subtitle2">Dockerfile Path</Typography>
              <Tooltip title="Path to your Dockerfile relative to the repository root">
                <IconButton size="small">
                  <HelpIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
            <TextField
              fullWidth
              variant="outlined"
              size="small"
              value={config.dockerfilePath || ''}
              onChange={(e) => handleConfigChange('dockerfilePath', e.target.value)}
              placeholder={preset.defaultConfig.dockerfilePath}
            />
          </Box>
        )}
      </Box>
    );
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Build Configuration
      </Typography>
      <Typography variant="body2" color="textSecondary" paragraph>
        Select the build preset that matches your service technology
      </Typography>

      <FormControl component="fieldset" fullWidth>
        <RadioGroup
          value={data.buildPreset || ''}
          onChange={handlePresetChange}
        >
          <Grid container spacing={2}>
            {SERVICE_BUILD_PRESETS.map((preset) => (
              <Grid item xs={12} sm={6} md={4} key={preset.id}>
                {renderPresetCard(preset)}
              </Grid>
            ))}
          </Grid>
        </RadioGroup>
      </FormControl>

      {renderConfigurationFields()}
    </Box>
  );
};
