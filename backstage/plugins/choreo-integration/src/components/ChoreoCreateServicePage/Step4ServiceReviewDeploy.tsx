import React from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Divider,
  Chip,
} from '@material-ui/core';
import { ServiceCreationData } from '../../api';
import { getServicePresetById } from './serviceBuildPresets';

interface Step4ServiceReviewDeployProps {
  data: ServiceCreationData;
  orgHandler: string;
  projectId: string;
}

export const Step4ServiceReviewDeploy: React.FC<Step4ServiceReviewDeployProps> = ({
  data,
  orgHandler,
  projectId,
}) => {
  const selectedPreset = getServicePresetById(data.buildPreset);

  const renderConfigValue = (label: string, value: string | number | undefined, defaultValue?: string | number) => {
    const displayValue = value || defaultValue || 'Not specified';
    return (
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
        <Typography variant="body2" color="textSecondary">
          {label}:
        </Typography>
        <Typography variant="body2">
          {displayValue}
        </Typography>
      </Box>
    );
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Review & Deploy
      </Typography>
      <Typography variant="body2" color="textSecondary" paragraph>
        Review your service configuration before deployment
      </Typography>

      {/* Repository Configuration */}
      <Card style={{ marginBottom: 16 }}>
        <CardContent>
          <Typography variant="subtitle1" gutterBottom style={{ fontWeight: 600 }}>
            Repository Configuration
          </Typography>
          <Box mb={2}>
            <Typography variant="body2" color="textSecondary" gutterBottom>
              Repository URL:
            </Typography>
            <Typography variant="body2" style={{ wordBreak: 'break-all' }}>
              {data.repositoryUrl}
            </Typography>
          </Box>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
            <Typography variant="body2" color="textSecondary">
              Branch:
            </Typography>
            <Chip label={data.branch} size="small" />
          </Box>
          {data.componentDirectory && (
            <Box display="flex" justifyContent="space-between" alignItems="center">
              <Typography variant="body2" color="textSecondary">
                Component Directory:
              </Typography>
              <Typography variant="body2">
                {data.componentDirectory}
              </Typography>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Component Details */}
      <Card style={{ marginBottom: 16 }}>
        <CardContent>
          <Typography variant="subtitle1" gutterBottom style={{ fontWeight: 600 }}>
            Component Details
          </Typography>
          <Box mb={2}>
            <Typography variant="body2" color="textSecondary" gutterBottom>
              Display Name:
            </Typography>
            <Typography variant="body2">
              {data.displayName}
            </Typography>
          </Box>
          <Box mb={2}>
            <Typography variant="body2" color="textSecondary" gutterBottom>
              Component Name:
            </Typography>
            <Typography variant="body2" style={{ fontFamily: 'monospace' }}>
              {data.componentName}
            </Typography>
          </Box>
          {data.description && (
            <Box>
              <Typography variant="body2" color="textSecondary" gutterBottom>
                Description:
              </Typography>
              <Typography variant="body2">
                {data.description}
              </Typography>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Build Configuration */}
      <Card style={{ marginBottom: 16 }}>
        <CardContent>
          <Typography variant="subtitle1" gutterBottom style={{ fontWeight: 600 }}>
            Build Configuration
          </Typography>
          <Box mb={2}>
            <Typography variant="body2" color="textSecondary" gutterBottom>
              Build Preset:
            </Typography>
            <Box display="flex" alignItems="center">
              <Chip 
                label={selectedPreset?.name || data.buildPreset} 
                size="small" 
                style={{ marginRight: 8 }}
              />
              <Typography variant="body2" color="textSecondary">
                {selectedPreset?.description}
              </Typography>
            </Box>
          </Box>
          
          <Divider style={{ margin: '12px 0' }} />
          
          <Typography variant="body2" color="textSecondary" gutterBottom style={{ fontWeight: 600 }}>
            Configuration Details:
          </Typography>
          
          {data.buildConfig.languageVersion && (
            renderConfigValue(
              'Language Version', 
              data.buildConfig.languageVersion,
              selectedPreset?.defaultConfig.languageVersion
            )
          )}
          
          {renderConfigValue(
            'Port', 
            data.buildConfig.port,
            selectedPreset?.defaultConfig.port
          )}
          
          {data.buildConfig.runCommand && (
            renderConfigValue(
              'Run Command', 
              data.buildConfig.runCommand,
              selectedPreset?.defaultConfig.runCommand
            )
          )}
          
          {data.buildConfig.dockerfilePath && (
            renderConfigValue(
              'Dockerfile Path', 
              data.buildConfig.dockerfilePath,
              selectedPreset?.defaultConfig.dockerfilePath
            )
          )}
        </CardContent>
      </Card>

      {/* Deployment Context */}
      <Card style={{ marginBottom: 16 }}>
        <CardContent>
          <Typography variant="subtitle1" gutterBottom style={{ fontWeight: 600 }}>
            Deployment Context
          </Typography>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
            <Typography variant="body2" color="textSecondary">
              Organization:
            </Typography>
            <Typography variant="body2">
              {orgHandler}
            </Typography>
          </Box>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="body2" color="textSecondary">
              Project:
            </Typography>
            <Typography variant="body2">
              {projectId}
            </Typography>
          </Box>
        </CardContent>
      </Card>

      {/* What happens next section with accessible contrast */}
      <Box
        mt={3}
        p={2}
        style={{
          backgroundColor: '#1565C0', // Darker blue for better contrast
          color: '#ffffff',
          borderRadius: 4,
          border: '1px solid #0d47a1'
        }}
      >
        <Typography variant="body2" style={{ color: '#ffffff', lineHeight: 1.6 }}>
          <strong style={{ color: '#ffffff' }}>What happens next?</strong>
          <br />
          1. Your service component will be created in the Choreo Console
          <br />
          2. The initial build will be triggered automatically
          <br />
          3. Once built, your service will be deployed to the development environment
          <br />
          4. You can monitor the progress in the Choreo Console
        </Typography>
      </Box>
    </Box>
  );
};
