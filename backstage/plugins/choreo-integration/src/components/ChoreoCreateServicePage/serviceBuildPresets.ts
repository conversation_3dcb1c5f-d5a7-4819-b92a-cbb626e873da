import { ServiceBuildPreset } from '../../api';

export const SERVICE_BUILD_PRESETS: ServiceBuildPreset[] = [
  {
    id: 'nodejs',
    name: 'Node.js',
    description: 'Node.js server application',
    buildpackId: 'nodejs',
    defaultConfig: {
      languageVersion: '20.x.x',
      port: 3000,
      runCommand: 'npm start',
    },
  },
  {
    id: 'python',
    name: 'Python',
    description: 'Python server application (Flask/Django/FastAPI)',
    buildpackId: 'python',
    defaultConfig: {
      languageVersion: '3.11',
      port: 8000,
      runCommand: 'python app.py',
    },
  },
  {
    id: 'java',
    name: 'Java',
    description: 'Java server application (Spring Boot/Quarkus)',
    buildpackId: 'java',
    defaultConfig: {
      languageVersion: '17',
      port: 8080,
    },
  },
  {
    id: 'go',
    name: 'Go',
    description: 'Go server application',
    buildpackId: 'go',
    defaultConfig: {
      languageVersion: '1.21',
      port: 8080,
    },
  },
  {
    id: 'dotnet',
    name: '.NET',
    description: '.NET Core server application',
    buildpackId: 'dotnet',
    defaultConfig: {
      languageVersion: '8.0',
      port: 5000,
    },
  },
  {
    id: 'ballerina',
    name: 'Ballerina',
    description: 'Ballerina server application',
    buildpackId: 'ballerina',
    defaultConfig: {
      languageVersion: '2201.8.0',
      port: 9090,
    },
  },
  {
    id: 'php',
    name: 'PHP',
    description: 'PHP server application',
    buildpackId: 'php',
    defaultConfig: {
      languageVersion: '8.2',
      port: 8080,
    },
  },
  {
    id: 'ruby',
    name: 'Ruby',
    description: 'Ruby server application (Rails/Sinatra)',
    buildpackId: 'ruby',
    defaultConfig: {
      languageVersion: '3.2',
      port: 3000,
    },
  },
  {
    id: 'docker',
    name: 'Docker',
    description: 'Custom Docker container',
    buildpackId: 'docker',
    defaultConfig: {
      dockerfilePath: 'Dockerfile',
      port: 8080,
    },
  },
  {
    id: 'wso2-mi',
    name: 'WSO2 Micro Integrator',
    description: 'WSO2 Micro Integrator service',
    buildpackId: 'wso2-mi',
    defaultConfig: {
      port: 8290,
    },
  },
  {
    id: 'prism-mock',
    name: 'Prism Mock',
    description: 'Prism API mock server',
    buildpackId: 'prism-mock',
    defaultConfig: {
      port: 4010,
    },
  },
];

export const getServicePresetById = (id: string): ServiceBuildPreset | undefined => {
  return SERVICE_BUILD_PRESETS.find(preset => preset.id === id);
};

export const getDefaultServicePreset = (): ServiceBuildPreset => {
  return SERVICE_BUILD_PRESETS[0]; // Default to Node.js
};
