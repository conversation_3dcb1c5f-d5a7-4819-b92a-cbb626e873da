import React, { useState, useCallback } from 'react';
import { usePara<PERSON>, useNavigate, useLocation } from 'react-router-dom';
import {
  Typography,
  Box,
  IconButton,
  Paper,
  Stepper,
  Step,
  StepLabel,
  Button,
  CircularProgress,
  Snackbar,
} from '@material-ui/core';
import { Alert } from '@material-ui/lab';
import { ArrowBack as ArrowBackIcon } from '@material-ui/icons';
import { useRouteRef, useApi } from '@backstage/core-plugin-api';
import { createComponentRouteRef, componentsRouteRef } from '../../routes';
import { choreoApiRef, ServiceCreationData, ServiceBuildConfig } from '../../api';
import { Step1RepositoryConfig } from '../ChoreoCreateWebAppPage/Step1RepositoryConfig';
import { Step2ComponentDetails } from '../ChoreoCreateWebAppPage/Step2ComponentDetails';
import { Step3ServiceBuildConfig } from './Step3ServiceBuildConfig';
import { Step4ServiceReviewDeploy } from './Step4ServiceReviewDeploy';
import { getDefaultServicePreset } from './serviceBuildPresets';
import {
  validateStep1,
  validateStep2,
  validateStep3,
  validateAllSteps,
  ValidationError,
  ValidationResult
} from '../ChoreoCreateWebAppPage/validation';

const STEPS = [
  'Repository Configuration',
  'Component Details',
  'Build Configuration',
  'Review & Deploy'
];

interface LocationState {
  orgId?: number;
}

export const ChoreoCreateServicePage = () => {
  const { orgHandler, projectId } = useParams<{
    orgHandler: string;
    projectId: string;
  }>();
  const navigate = useNavigate();
  const location = useLocation();
  const state = location.state as LocationState;
  const orgId = state?.orgId;

  const choreoApi = useApi(choreoApiRef);
  const createComponentRoute = useRouteRef(createComponentRouteRef);
  const componentsRoute = useRouteRef(componentsRouteRef);

  // Stepper state
  const [activeStep, setActiveStep] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<ValidationError[]>([]);
  const [errorMessage, setErrorMessage] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  // Form data state
  const [formData, setFormData] = useState<Partial<ServiceCreationData>>({
    repositoryUrl: '',
    branch: 'main',
    componentDirectory: '',
    displayName: '',
    componentName: '',
    description: '',
    buildPreset: getDefaultServicePreset().id,
    buildConfig: getDefaultServicePreset().defaultConfig,
    orgId: orgId,
    orgHandler: orgHandler || '',
    projectId: projectId || '',
  });

  const handleBackToCreate = () => {
    if (orgHandler && projectId) {
      navigate(createComponentRoute({ orgHandler, projectId }));
    }
  };

  const handleFieldChange = useCallback((field: keyof ServiceCreationData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
    
    // Clear errors for this field
    setErrors(prev => prev.filter(error => error.field !== field));
  }, []);

  const handleBuildConfigChange = useCallback((config: ServiceBuildConfig) => {
    setFormData(prev => ({
      ...prev,
      buildConfig: config,
    }));
  }, []);

  const validateCurrentStep = (): boolean => {
    let stepErrors: ValidationError[] = [];

    switch (activeStep) {
      case 0:
        const step1Result = validateStep1(formData as any);
        stepErrors = step1Result.errors;
        break;
      case 1:
        const step2Result = validateStep2(formData as any);
        stepErrors = step2Result.errors;
        break;
      case 2:
        const step3Result = validateStep3(formData as any);
        stepErrors = step3Result.errors;
        break;
      case 3:
        const allStepsResult = validateAllSteps(formData as any);
        stepErrors = allStepsResult.errors;
        break;
      default:
        break;
    }

    setErrors(stepErrors);
    return stepErrors.length === 0;
  };

  const handleNext = () => {
    if (validateCurrentStep()) {
      setActiveStep(prev => prev + 1);
    }
  };

  const handleBack = () => {
    setActiveStep(prev => prev - 1);
    setErrors([]);
  };

  const handleCreateComponent = async () => {
    if (!validateCurrentStep()) {
      return;
    }

    setIsLoading(true);
    setErrorMessage('');

    try {
      const response = await choreoApi.createServiceComponent(formData as ServiceCreationData);

      setSuccessMessage(`Service "${formData.displayName}" created successfully!`);

      // Navigate to components page after a short delay
      setTimeout(() => {
        if (orgHandler && projectId) {
          navigate(componentsRoute({ orgHandler, projectId }));
        }
      }, 2000);

    } catch (error) {
      console.error('Failed to create service component:', error);
      setErrorMessage(
        error instanceof Error
          ? error.message
          : 'Failed to create service. Please try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  const renderStepContent = () => {
    switch (activeStep) {
      case 0:
        return (
          <Step1RepositoryConfig
            data={formData}
            onChange={handleFieldChange}
            errors={errors || []}
          />
        );
      case 1:
        return (
          <Step2ComponentDetails
            data={formData}
            onChange={handleFieldChange}
            errors={errors || []}
          />
        );
      case 2:
        return (
          <Step3ServiceBuildConfig
            data={formData}
            onChange={handleFieldChange}
            onBuildConfigChange={handleBuildConfigChange}
            errors={errors || []}
          />
        );
      case 3:
        return (
          <Step4ServiceReviewDeploy
            data={formData as ServiceCreationData}
            orgHandler={orgHandler || ''}
            projectId={projectId || ''}
          />
        );
      default:
        return null;
    }
  };

  return (
    <Box p={3}>
      <Box mb={3} display="flex" alignItems="center">
        <IconButton
          onClick={handleBackToCreate}
          style={{ marginRight: 16 }}
          aria-label="back to create component"
        >
          <ArrowBackIcon />
        </IconButton>
        <Box>
          <Typography variant="h4" gutterBottom>
            Create a Service
          </Typography>
          <Typography variant="body1" color="textSecondary">
            Configure your new service component
          </Typography>
        </Box>
      </Box>

      <Paper style={{ padding: 24 }}>
        {/* Stepper */}
        <Stepper activeStep={activeStep} alternativeLabel style={{ marginBottom: 32 }}>
          {STEPS.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {/* Step Content */}
        <Box minHeight={400}>
          {renderStepContent()}
        </Box>

        {/* Navigation Buttons */}
        <Box display="flex" justifyContent="space-between" mt={4}>
          <Button
            disabled={activeStep === 0}
            onClick={handleBack}
            variant="outlined"
          >
            Back
          </Button>
          
          <Box>
            {activeStep === STEPS.length - 1 ? (
              <Button
                variant="contained"
                color="primary"
                onClick={handleCreateComponent}
                disabled={isLoading}
                startIcon={isLoading ? <CircularProgress size={20} /> : undefined}
              >
                {isLoading ? 'Creating...' : 'Create Service'}
              </Button>
            ) : (
              <Button
                variant="contained"
                color="primary"
                onClick={handleNext}
              >
                Next
              </Button>
            )}
          </Box>
        </Box>
      </Paper>

      {/* Success/Error Messages */}
      <Snackbar
        open={!!successMessage}
        autoHideDuration={6000}
        onClose={() => setSuccessMessage('')}
      >
        <Alert onClose={() => setSuccessMessage('')} severity="success">
          {successMessage}
        </Alert>
      </Snackbar>

      <Snackbar
        open={!!errorMessage}
        autoHideDuration={6000}
        onClose={() => setErrorMessage('')}
      >
        <Alert onClose={() => setErrorMessage('')} severity="error">
          {errorMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};
