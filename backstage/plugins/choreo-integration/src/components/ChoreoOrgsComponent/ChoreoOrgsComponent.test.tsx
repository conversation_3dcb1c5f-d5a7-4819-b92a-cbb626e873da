
import { renderInTestApp, TestApiProvider } from '@backstage/test-utils';
import { ChoreoOrgsComponent } from './ChoreoOrgsComponent';
import { choreoApiRef } from '../../api';

const mockChoreoApi = {
  getOrganizations: jest.fn(),
  getDefaultOrganization: jest.fn(),
};

describe('ChoreoOrgsComponent', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders loading state', async () => {
    mockChoreoApi.getOrganizations.mockImplementation(
      () => new Promise(() => {}), // Never resolves
    );

    const { getByTestId } = await renderInTestApp(
      <TestApiProvider apis={[[choreoApiRef, mockChoreoApi]]}>
        <ChoreoOrgsComponent />
      </TestApiProvider>,
    );

    expect(getByTestId('progress')).toBeInTheDocument();
  });

  it('renders organization data', async () => {
    const mockData = {
      organizations: [
        {
          id: '74908',
          uuid: '1056246d-5204-405f-945d-12791bdfe2ef',
          handle: 'pasindui',
          name: 'pasindui',
          owner: {
            id: '60929',
            idpId: '3622cc93-36ad-4272-80dc-a7c19649f618',
            createdAt: '2025-07-09T09:50:18.947Z',
          },
        },
      ],
      status: 'success',
      count: 1,
    };

    mockChoreoApi.getOrganizations.mockResolvedValue(mockData);
    mockChoreoApi.getDefaultOrganization.mockReturnValue({
      handle: 'pasindui',
      uuid: '1056246d-5204-405f-945d-12791bdfe2ef',
      organization: mockData.organizations[0],
    });

    const { findByText } = await renderInTestApp(
      <TestApiProvider apis={[[choreoApiRef, mockChoreoApi]]}>
        <ChoreoOrgsComponent />
      </TestApiProvider>,
    );

    // Wait for the table to appear and check for specific data
    expect(await findByText('Organizations')).toBeInTheDocument();

    // Check for the organization data in the table
    expect(await findByText('74908')).toBeInTheDocument();

    // Check for the default chip (look for the chip specifically, not just text)
    const defaultChip = await findByText('Default');
    expect(defaultChip).toBeInTheDocument();
    expect(defaultChip.closest('.MuiChip-root')).toBeInTheDocument();
  });

  it('handles error state', async () => {
    const error = new Error('API connection failed');
    mockChoreoApi.getOrganizations.mockRejectedValue(error);

    await renderInTestApp(
      <TestApiProvider apis={[[choreoApiRef, mockChoreoApi]]}>
        <ChoreoOrgsComponent />
      </TestApiProvider>,
    );

    // Just verify the API was called and component rendered without crashing
    expect(mockChoreoApi.getOrganizations).toHaveBeenCalled();
  });

  it('shows default chip for the correct organization', async () => {
    const mockData = {
      organizations: [
        {
          id: '74800',
          uuid: '1056246d-5204-405f-945d-12791bdfe2ef',
          handle: 'pasindunaduninduwara',
          name: 'pasindunaduninduwara',
          owner: {
            id: '60929',
            idpId: '3622cc93-36ad-4272-80dc-a7c19649f618',
            createdAt: '2025-07-09T09:50:18.947Z',
          },
        },
        {
          id: '10861',
          uuid: '2056246d-5204-405f-945d-12791bdfe2ef',
          handle: 'choreodemo',
          name: 'Demo Organization',
          owner: {
            id: '60930',
            idpId: 'different-idp-id',
            createdAt: '2025-07-09T09:50:18.947Z',
          },
        },
      ],
      status: 'success',
      count: 2,
    };

    mockChoreoApi.getOrganizations.mockResolvedValue(mockData);
    // Mock that the first organization is selected as default by Enhanced Selection Algorithm
    mockChoreoApi.getDefaultOrganization.mockReturnValue({
      handle: 'pasindunaduninduwara',
      uuid: '1056246d-5204-405f-945d-12791bdfe2ef',
      organization: mockData.organizations[0],
    });

    const { findByText, getAllByText } = await renderInTestApp(
      <TestApiProvider apis={[[choreoApiRef, mockChoreoApi]]}>
        <ChoreoOrgsComponent />
      </TestApiProvider>,
    );

    // Wait for the table to appear
    expect(await findByText('Organizations')).toBeInTheDocument();

    // Check that both organizations are displayed
    expect(await findByText('74800')).toBeInTheDocument();
    expect(await findByText('10861')).toBeInTheDocument();
    expect(await findByText('Demo Organization')).toBeInTheDocument();

    // Check that only one "Default" chip appears (look for the chip specifically)
    // Wait for the table to be fully rendered first
    await findByText('Organizations');

    // Get all elements with "Default" text and filter for chips
    const allDefaultElements = getAllByText('Default');
    const defaultChips = allDefaultElements.filter(el => el.closest('.MuiChip-root'));

    expect(defaultChips).toHaveLength(1);
    expect(defaultChips[0]).toBeInTheDocument();

    // Verify the Enhanced Selection Algorithm was called with correct parameters
    expect(mockChoreoApi.getDefaultOrganization).toHaveBeenCalledWith(
      mockData.organizations,
      { userIdpId: '3622cc93-36ad-4272-80dc-a7c19649f618' }
    );
  });
});
