
import { useApi } from '@backstage/core-plugin-api';
import {
  Table,
  TableColumn,
  Progress,
  ResponseErrorPanel,
  InfoCard,
} from '@backstage/core-components';
import { Chip } from '@material-ui/core';
import useAsync from 'react-use/lib/useAsync';
import { choreoApiRef, ChoreoOrg } from '../../api';

const columns: TableColumn[] = [
  { title: 'ID', field: 'id' },
  { title: 'Handle', field: 'handle' },
  { title: 'Name', field: 'name' },
  {
    title: 'Status',
    field: 'defaultIndicator',
    render: (rowData: any) => rowData.defaultIndicator || null,
    sorting: false,
    width: '100px'
  },
];

export const ChoreoOrgsComponent = () => {
  const choreoApi = useApi(choreoApiRef);

  const { value, loading, error } = useAsync(async () => {
    const response = await choreoApi.getOrganizations();
    return response.organizations;
  }, [choreoApi]);

  if (loading) {
    return <Progress />;
  }

  if (error) {
    return <ResponseErrorPanel error={error} />;
  }

  const organizations = value || [];

  // Use Enhanced Selection Algorithm to determine default organization
  // For now, we'll use a mock userIdpId - in a real implementation,
  // this would come from the authentication context
  // We'll simulate that the user's IDP ID matches the first organization's owner
  const mockUserIdpId = organizations.length > 0 ? organizations[0].owner.idpId : undefined;

  const defaultOrgResult = choreoApi.getDefaultOrganization(organizations, {
    userIdpId: mockUserIdpId
  });

  const defaultOrgId = defaultOrgResult?.organization?.id;

  const data = organizations.map((org: ChoreoOrg) => ({
    id: org.id,
    handle: org.handle,
    name: org.name,
    defaultIndicator: org.id === defaultOrgId ? (
      <Chip
        label="Default"
        size="small"
        color="primary"
        variant="outlined"
      />
    ) : null,
  }));

  return (
    <InfoCard title="Choreo Organizations">
      <Table
        title="Organizations"
        options={{ search: true, paging: false }}
        columns={columns}
        data={data}
      />
    </InfoCard>
  );
};
