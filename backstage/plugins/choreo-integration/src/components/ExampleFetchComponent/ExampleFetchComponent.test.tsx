import { renderInTestApp } from '@backstage/test-utils';
import { ExampleFetchComponent } from './ExampleFetchComponent';

describe('ExampleFetchComponent', () => {
  it('renders the user table', async () => {
    const { getAllByText, getByAltText, getByText, findByRole } =
      await renderInTestApp(<ExampleFetchComponent />);

    // Wait for the table to render
    const table = await findByRole('table');
    const nationality = getAllByText('GB');
    // Assert that the table contains the expected user data
    expect(table).toBeInTheDocument();
    expect(getByAltText('Carolyn')).toBeInTheDocument();
    expect(getByText('<PERSON>')).toBeInTheDocument();
    expect(getByText('<EMAIL>')).toBeInTheDocument();
    expect(nationality[0]).toBeInTheDocument();
  });
});
