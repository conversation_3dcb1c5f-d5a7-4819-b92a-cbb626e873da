import React, { useState } from 'react';
import { useAsync } from 'react-use';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Typography,
  Box,
  CircularProgress,
  Card,
  CardContent,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Grid,
} from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import { useApi } from '@backstage/core-plugin-api';
import { ErrorPanel, InfoCard } from '@backstage/core-components';
import { choreoApiRef, ChoreoOrg, DefaultOrgSelectionOptions } from '../../api/choreoApi';

const useStyles = makeStyles((theme) => ({
  root: {
    padding: theme.spacing(3),
  },
  card: {
    marginBottom: theme.spacing(3),
  },
  table: {
    minWidth: 650,
  },
  loadingContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: 200,
  },
  defaultOrgCard: {
    backgroundColor: theme.palette.primary.light,
    color: theme.palette.primary.contrastText,
  },
  formControl: {
    margin: theme.spacing(1),
    minWidth: 200,
  },
  priorityChip: {
    margin: theme.spacing(0.5),
  },
}));

export const ChoreoDefaultOrgComponent = () => {
  const classes = useStyles();
  const choreoApi = useApi(choreoApiRef);
  
  // Form state for testing default org selection
  const [testOptions, setTestOptions] = useState<DefaultOrgSelectionOptions>({});
  const [selectedTestOption, setSelectedTestOption] = useState<string>('');

  const { value: orgsData, loading, error } = useAsync(async () => {
    const response = await choreoApi.getOrganizations();
    return response;
  }, []);

  const organizations = orgsData?.organizations || [];
  
  // Get default organization with current test options
  const defaultOrgResult = organizations.length > 0 
    ? choreoApi.getDefaultOrganization(organizations, testOptions)
    : null;

  const handleTestOptionChange = (field: keyof DefaultOrgSelectionOptions, value: string) => {
    setTestOptions(prev => ({
      ...prev,
      [field]: value || undefined,
    }));
  };

  const clearTestOptions = () => {
    setTestOptions({});
    setSelectedTestOption('');
  };

  const getSelectionPriority = (): string => {
    if (testOptions.userIdpId) {
      return 'Owner Match (Priority 1)';
    }
    if (testOptions.returnToOrg || testOptions.selectedOrgHandle) {
      return 'URL/Return Parameter (Priority 2)';
    }
    if (testOptions.sessionStoredOrg) {
      return 'Session Stored (Priority 3)';
    }
    return 'First Organization Fallback (Priority 4)';
  };

  if (loading) {
    return (
      <Box className={classes.loadingContainer}>
        <CircularProgress />
        <Typography variant="h6" style={{ marginLeft: 16 }}>
          Loading Choreo organizations...
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <ErrorPanel
        title="Failed to load organizations"
        defaultExpanded
        error={error}
      />
    );
  }

  if (!organizations.length) {
    return (
      <InfoCard title="No Organizations">
        <Typography>No organizations found.</Typography>
      </InfoCard>
    );
  }

  return (
    <Box className={classes.root}>
      <Typography variant="h4" gutterBottom>
        Choreo Default Organization Selection
      </Typography>
      
      <Typography variant="body1" paragraph>
        This component demonstrates the default organization selection logic based on Choreo Console's approach.
        Test different scenarios using the controls below.
      </Typography>

      {/* Default Organization Result */}
      {defaultOrgResult && (
        <Card className={`${classes.card} ${classes.defaultOrgCard}`}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Selected Default Organization
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Typography variant="body1">
                  <strong>Handle:</strong> {defaultOrgResult.handle}
                </Typography>
                <Typography variant="body1">
                  <strong>Name:</strong> {defaultOrgResult.organization.name}
                </Typography>
                <Typography variant="body1">
                  <strong>UUID:</strong> {defaultOrgResult.uuid}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body1">
                  <strong>Selection Logic:</strong>
                </Typography>
                <Chip 
                  label={getSelectionPriority()} 
                  className={classes.priorityChip}
                  color="secondary"
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      )}

      {/* Test Controls */}
      <Card className={classes.card}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Test Default Organization Selection
          </Typography>
          <Typography variant="body2" paragraph>
            Modify the options below to see how the default organization selection logic works.
            Priority order: Owner Match (idpId) → URL Parameter → Session Storage → First Organization
          </Typography>
          
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={3}>
              <TextField
                label="Return To Org"
                value={testOptions.returnToOrg || ''}
                onChange={(e) => handleTestOptionChange('returnToOrg', e.target.value)}
                className={classes.formControl}
                helperText="Priority 2: URL parameter"
                fullWidth
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <TextField
                label="Selected Org Handle"
                value={testOptions.selectedOrgHandle || ''}
                onChange={(e) => handleTestOptionChange('selectedOrgHandle', e.target.value)}
                className={classes.formControl}
                helperText="Priority 2: URL selection"
                fullWidth
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <TextField
                label="Session Stored Org"
                value={testOptions.sessionStoredOrg || ''}
                onChange={(e) => handleTestOptionChange('sessionStoredOrg', e.target.value)}
                className={classes.formControl}
                helperText="Priority 3: Session storage"
                fullWidth
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <TextField
                label="User IDP ID"
                value={testOptions.userIdpId || ''}
                onChange={(e) => handleTestOptionChange('userIdpId', e.target.value)}
                className={classes.formControl}
                helperText="Priority 1: Owner match"
                fullWidth
              />
            </Grid>
          </Grid>
          
          <Box mt={2}>
            <Button 
              variant="outlined" 
              onClick={clearTestOptions}
              style={{ marginRight: 8 }}
            >
              Clear All Options
            </Button>
            <FormControl className={classes.formControl}>
              <InputLabel>Quick Test</InputLabel>
              <Select
                value={selectedTestOption}
                onChange={(e) => {
                  const value = e.target.value as string;
                  setSelectedTestOption(value);
                  if (value === 'first-org' && organizations[0]) {
                    handleTestOptionChange('returnToOrg', organizations[0].handle);
                  } else if (value === 'owner-match' && organizations[0]) {
                    handleTestOptionChange('userIdpId', organizations[0].owner.idpId);
                  } else if (value === 'session' && organizations[0]) {
                    handleTestOptionChange('sessionStoredOrg', organizations[0].handle);
                  }
                }}
              >
                <MenuItem value="">None</MenuItem>
                <MenuItem value="first-org">Test First Org (URL)</MenuItem>
                <MenuItem value="owner-match">Test Owner Match</MenuItem>
                <MenuItem value="session">Test Session Storage</MenuItem>
              </Select>
            </FormControl>
          </Box>
        </CardContent>
      </Card>

      {/* Organizations Table */}
      <Card className={classes.card}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Available Organizations ({organizations.length})
          </Typography>
          <TableContainer component={Paper}>
            <Table className={classes.table}>
              <TableHead>
                <TableRow>
                  <TableCell><strong>ID</strong></TableCell>
                  <TableCell><strong>Handle</strong></TableCell>
                  <TableCell><strong>Name</strong></TableCell>
                  <TableCell><strong>UUID</strong></TableCell>
                  <TableCell><strong>Owner ID</strong></TableCell>
                  <TableCell><strong>Created</strong></TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {organizations.map((org) => (
                  <TableRow 
                    key={org.id}
                    style={{
                      backgroundColor: defaultOrgResult?.handle === org.handle 
                        ? 'rgba(25, 118, 210, 0.1)' 
                        : 'inherit'
                    }}
                  >
                    <TableCell>{org.id}</TableCell>
                    <TableCell>
                      {org.handle}
                      {defaultOrgResult?.handle === org.handle && (
                        <Chip 
                          label="DEFAULT" 
                          size="small" 
                          color="primary" 
                          style={{ marginLeft: 8 }}
                        />
                      )}
                    </TableCell>
                    <TableCell>{org.name}</TableCell>
                    <TableCell>{org.uuid}</TableCell>
                    <TableCell>{org.owner.id}</TableCell>
                    <TableCell>{new Date(org.owner.createdAt).toLocaleDateString()}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </Box>
  );
};
