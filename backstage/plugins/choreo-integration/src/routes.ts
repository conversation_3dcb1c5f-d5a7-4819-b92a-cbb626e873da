import { createRouteRef } from '@backstage/core-plugin-api';

export const rootRouteRef = createRouteRef({
  id: 'choreo-integration',
});

export const componentsRouteRef = createRouteRef({
  id: 'choreo-integration-components',
  params: ['orgHandler', 'projectId'],
});

export const createComponentRouteRef = createRouteRef({
  id: 'choreo-integration-create-component',
  params: ['orgHandler', 'projectId'],
});

export const createMicroserviceRouteRef = createRouteRef({
  id: 'choreo-integration-create-microservice',
  params: ['orgHandler', 'projectId'],
});

export const createWebAppRouteRef = createRouteRef({
  id: 'choreo-integration-create-webapp',
  params: ['orgHandler', 'projectId'],
});

export const createServiceRouteRef = createRouteRef({
  id: 'choreo-integration-create-service',
  params: ['orgHandler', 'projectId'],
});
