import { ChoreoClient } from './ChoreoClient';
import { ChoreoOrg, DefaultOrgSelectionOptions } from './choreoApi';

// Mock the dependencies
const mockDiscoveryApi = {
  getBaseUrl: jest.fn(),
};

const mockFetchApi = {
  fetch: jest.fn(),
};

describe('ChoreoClient', () => {
  let client: ChoreoClient;

  beforeEach(() => {
    client = new ChoreoClient({
      discoveryApi: mockDiscoveryApi as any,
      fetchApi: mockFetchApi as any,
    });
    jest.clearAllMocks();
  });

  describe('getDefaultOrganization', () => {
    const mockOrganizations: ChoreoOrg[] = [
      {
        id: '1',
        uuid: 'uuid-1',
        handle: 'org1',
        name: 'Organization 1',
        owner: {
          id: 'owner-1',
          idpId: 'user-idp-1',
          createdAt: '2023-01-01T00:00:00Z',
        },
      },
      {
        id: '2',
        uuid: 'uuid-2',
        handle: 'org2',
        name: 'Organization 2',
        owner: {
          id: 'owner-2',
          idpId: 'user-idp-2',
          createdAt: '2023-01-02T00:00:00Z',
        },
      },
      {
        id: '3',
        uuid: 'uuid-3',
        handle: 'org3',
        name: 'Organization 3',
        owner: {
          id: 'owner-3',
          idpId: 'user-idp-3',
          createdAt: '2023-01-03T00:00:00Z',
        },
      },
    ];

    it('should return null for empty organizations array', () => {
      const result = client.getDefaultOrganization([]);
      expect(result).toBeNull();
    });

    it('should return null for null organizations', () => {
      const result = client.getDefaultOrganization(null as any);
      expect(result).toBeNull();
    });

    it('should prioritize owner match using userIdpId (Priority 1)', () => {
      const options: DefaultOrgSelectionOptions = {
        returnToOrg: 'org2',
        sessionStoredOrg: 'org3',
        userIdpId: 'user-idp-1',
      };

      const result = client.getDefaultOrganization(mockOrganizations, options);

      expect(result).toEqual({
        handle: 'org1',
        uuid: 'uuid-1',
        organization: mockOrganizations[0],
      });
    });

    it('should fall back to returnToOrg parameter (Priority 2) when no owner match', () => {
      const options: DefaultOrgSelectionOptions = {
        returnToOrg: 'org2',
        sessionStoredOrg: 'org3',
        userIdpId: 'nonexistent-idp',
      };

      const result = client.getDefaultOrganization(mockOrganizations, options);

      expect(result).toEqual({
        handle: 'org2',
        uuid: 'uuid-2',
        organization: mockOrganizations[1],
      });
    });

    it('should fall back to selectedOrgHandle parameter (Priority 2) when no owner match', () => {
      const options: DefaultOrgSelectionOptions = {
        selectedOrgHandle: 'org3',
        sessionStoredOrg: 'org1',
        userIdpId: 'nonexistent-idp',
      };

      const result = client.getDefaultOrganization(mockOrganizations, options);

      expect(result).toEqual({
        handle: 'org3',
        uuid: 'uuid-3',
        organization: mockOrganizations[2],
      });
    });

    it('should return null if returnToOrg is specified but not found', () => {
      const options: DefaultOrgSelectionOptions = {
        returnToOrg: 'nonexistent-org',
        userIdpId: 'nonexistent-idp',
      };

      const result = client.getDefaultOrganization(mockOrganizations, options);
      expect(result).toBeNull();
    });

    it('should fall back to sessionStoredOrg (Priority 3)', () => {
      const options: DefaultOrgSelectionOptions = {
        sessionStoredOrg: 'org2',
        userIdpId: 'nonexistent-idp',
      };

      const result = client.getDefaultOrganization(mockOrganizations, options);

      expect(result).toEqual({
        handle: 'org2',
        uuid: 'uuid-2',
        organization: mockOrganizations[1],
      });
    });

    it('should fall back to first organization (Priority 4)', () => {
      const options: DefaultOrgSelectionOptions = {};

      const result = client.getDefaultOrganization(mockOrganizations, options);

      expect(result).toEqual({
        handle: 'org1',
        uuid: 'uuid-1',
        organization: mockOrganizations[0],
      });
    });

    it('should handle missing sessionStoredOrg gracefully', () => {
      const options: DefaultOrgSelectionOptions = {
        sessionStoredOrg: 'nonexistent-org',
        userIdpId: 'user-idp-3',
      };

      const result = client.getDefaultOrganization(mockOrganizations, options);

      // Should fall back to owner match (Priority 1)
      expect(result).toEqual({
        handle: 'org3',
        uuid: 'uuid-3',
        organization: mockOrganizations[2],
      });
    });

    it('should handle missing userIdpId gracefully', () => {
      const options: DefaultOrgSelectionOptions = {
        sessionStoredOrg: 'nonexistent-org',
        userIdpId: 'nonexistent-idp',
      };

      const result = client.getDefaultOrganization(mockOrganizations, options);

      // Should fall back to first organization (Priority 4)
      expect(result).toEqual({
        handle: 'org1',
        uuid: 'uuid-1',
        organization: mockOrganizations[0],
      });
    });

    it('should work with single organization', () => {
      const singleOrg = [mockOrganizations[0]];
      const options: DefaultOrgSelectionOptions = {
        userIdpId: 'nonexistent-idp',
      };

      const result = client.getDefaultOrganization(singleOrg, options);

      expect(result).toEqual({
        handle: 'org1',
        uuid: 'uuid-1',
        organization: mockOrganizations[0],
      });
    });

    it('should prioritize owner match over URL parameters', () => {
      const options: DefaultOrgSelectionOptions = {
        returnToOrg: 'org1',
        userIdpId: 'user-idp-2', // This should match org2 and win over URL
      };

      const result = client.getDefaultOrganization(mockOrganizations, options);

      expect(result).toEqual({
        handle: 'org2',
        uuid: 'uuid-2',
        organization: mockOrganizations[1],
      });
    });

    it('should prioritize owner match over session storage', () => {
      const options: DefaultOrgSelectionOptions = {
        sessionStoredOrg: 'org1',
        userIdpId: 'user-idp-2', // This should match org2 and win over session
      };

      const result = client.getDefaultOrganization(mockOrganizations, options);

      expect(result).toEqual({
        handle: 'org2',
        uuid: 'uuid-2',
        organization: mockOrganizations[1],
      });
    });
  });
});
