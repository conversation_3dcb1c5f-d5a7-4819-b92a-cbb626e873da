import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@backstage/core-plugin-api';
import {
  ChoreoApi,
  ChoreoOrgsResponse,
  ChoreoProjectsResponse,
  ChoreoOrg,
  ChoreoComponentsResponse,
  ChoreoEnvironmentsResponse,
  DefaultOrgSelectionOptions,
  DefaultOrgResult,
  ProjectCreationEligibilityResponse,
  WebAppCreationData,
  ServiceCreationData,
  ComponentCreationResponse,
  RepositoryValidationRequest,
  RepositoryValidationResponse
} from './choreoApi';

export class ChoreoClient implements ChoreoApi {
  private readonly discoveryApi: DiscoveryApi;
  private readonly fetchApi: FetchApi;

  constructor(options: {
    discoveryApi: DiscoveryApi;
    fetchApi: FetchApi;
  }) {
    this.discoveryApi = options.discoveryApi;
    this.fetchApi = options.fetchApi;
  }

  async getOrganizations(): Promise<ChoreoOrgsResponse> {
    const baseUrl = await this.discoveryApi.getBaseUrl('choreo-integration');
    const response = await this.fetchApi.fetch(`${baseUrl}/choreo-orgs`);

    if (!response.ok) {
      throw new Error(
        `Failed to fetch Choreo organizations: ${response.status} ${response.statusText}`,
      );
    }

    return response.json();
  }

  async getProjects(orgId: number, orgHandler: string): Promise<ChoreoProjectsResponse> {
    const baseUrl = await this.discoveryApi.getBaseUrl('choreo-integration');
    const url = `${baseUrl}/choreo-projects`;

    const response = await this.fetchApi.fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        orgId,
        orgHandler,
      }),
    });

    if (!response.ok) {
      throw new Error(
        `Failed to fetch Choreo projects: ${response.status} ${response.statusText}`,
      );
    }

    return response.json();
  }

  async getProjectCreationEligibility(orgId: number, orgHandler: string): Promise<ProjectCreationEligibilityResponse> {
    const baseUrl = await this.discoveryApi.getBaseUrl('choreo-integration');
    const url = `${baseUrl}/choreo-project-creation-eligibility`;

    const response = await this.fetchApi.fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        orgId,
        orgHandler,
      }),
    });

    if (!response.ok) {
      throw new Error(
        `Failed to fetch project creation eligibility: ${response.status} ${response.statusText}`,
      );
    }

    return response.json();
  }

  async getComponents(orgHandler: string, projectId: string): Promise<ChoreoComponentsResponse> {
    const baseUrl = await this.discoveryApi.getBaseUrl('choreo-integration');
    const url = `${baseUrl}/choreo-components`;

    const response = await this.fetchApi.fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        orgHandler,
        projectId,
      }),
    });

    if (!response.ok) {
      throw new Error(
        `Failed to fetch components: ${response.status} ${response.statusText}`,
      );
    }

    return response.json();
  }

  async getEnvironments(orgUuid: string, projectId: string, type: string = 'external'): Promise<ChoreoEnvironmentsResponse> {
    const baseUrl = await this.discoveryApi.getBaseUrl('choreo-integration');
    const url = `${baseUrl}/choreo-environments`;

    const response = await this.fetchApi.fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        orgUuid,
        projectId,
        type,
      }),
    });

    if (!response.ok) {
      throw new Error(
        `Failed to fetch environments: ${response.status} ${response.statusText}`,
      );
    }

    return response.json();
  }

  /**
   * Determines the default organization using Enhanced Selection Algorithm
   * Priority order:
   * 1. Organization where user is the owner (using owner.idpId comparison)
   * 2. URL/Return Parameter Organization (returnToOrg or selectedOrgHandle)
   * 3. Session Stored Organization (sessionStoredOrg)
   * 4. First Organization (fallback)
   */
  getDefaultOrganization(
    organizations: ChoreoOrg[],
    options: DefaultOrgSelectionOptions = {}
  ): DefaultOrgResult | null {
    if (!organizations || organizations.length === 0) {
      return null;
    }

    const {
      returnToOrg,
      selectedOrgHandle,
      sessionStoredOrg,
      userIdpId
    } = options;

    let selectedOrg: ChoreoOrg | null = null;

    // Priority 1: Organization where user is the owner (using owner.idpId comparison)
    if (userIdpId) {
      const ownedOrgMatch = organizations.find(
        (org) => org.owner.idpId === userIdpId
      );
      if (ownedOrgMatch) {
        selectedOrg = ownedOrgMatch;
      }
    }

    // Priority 2: URL/Return Parameter Organization
    if (!selectedOrg && (returnToOrg || selectedOrgHandle)) {
      const returnOrgMatch = organizations.find(
        (org) => org.handle === returnToOrg || org.handle === selectedOrgHandle
      );
      if (returnOrgMatch) {
        selectedOrg = returnOrgMatch;
      }
      // If specific org requested but not found, return null (error state)
      else {
        return null;
      }
    }
    // Priority 3: Session Stored Organization
    else if (!selectedOrg && sessionStoredOrg) {
      const sessionUserOrgMatch = organizations.find(
        (org) => org.handle === sessionStoredOrg
      );
      if (sessionUserOrgMatch) {
        selectedOrg = sessionUserOrgMatch;
      }
    }

    // Priority 4: First Organization (fallback)
    if (!selectedOrg) {
      selectedOrg = organizations[0];
    }

    return {
      handle: selectedOrg.handle,
      uuid: selectedOrg.uuid,
      organization: selectedOrg
    };
  }

  async validateRepository(request: RepositoryValidationRequest): Promise<RepositoryValidationResponse> {
    const baseUrl = await this.discoveryApi.getBaseUrl('choreo-integration');
    const url = `${baseUrl}/choreo-validate-repository`;

    const response = await this.fetchApi.fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      throw new Error(
        `Failed to validate repository: ${response.status} ${response.statusText}`,
      );
    }

    return response.json();
  }

  async createWebAppComponent(data: WebAppCreationData): Promise<ComponentCreationResponse> {
    const baseUrl = await this.discoveryApi.getBaseUrl('choreo-integration');
    const url = `${baseUrl}/choreo-create-component`;

    // Send data in the format expected by the backend
    const requestData = {
      repositoryUrl: data.repositoryUrl,
      branch: data.branch,
      componentDirectory: data.componentDirectory,
      displayName: data.displayName,
      componentName: data.componentName,
      description: data.description,
      buildPreset: data.buildPreset,
      buildConfig: data.buildConfig,
      orgId: data.orgId, // Include orgId
      orgHandler: data.orgHandler,
      projectId: data.projectId,
    };

    const response = await this.fetchApi.fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData),
    });

    if (!response.ok) {
      throw new Error(
        `Failed to create web app component: ${response.status} ${response.statusText}`,
      );
    }

    return response.json();
  }

  async createServiceComponent(data: ServiceCreationData): Promise<ComponentCreationResponse> {
    const baseUrl = await this.discoveryApi.getBaseUrl('choreo-integration');
    const url = `${baseUrl}/choreo-create-service`;

    // Send data in the format expected by the backend
    const requestData = {
      repositoryUrl: data.repositoryUrl,
      branch: data.branch,
      componentDirectory: data.componentDirectory,
      displayName: data.displayName,
      componentName: data.componentName,
      description: data.description,
      buildPreset: data.buildPreset,
      buildConfig: data.buildConfig,
      orgId: data.orgId,
      orgHandler: data.orgHandler,
      projectId: data.projectId,
    };

    const response = await this.fetchApi.fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.error ||
        `Failed to create service component: ${response.status} ${response.statusText}`
      );
    }

    return response.json();
  }
}
