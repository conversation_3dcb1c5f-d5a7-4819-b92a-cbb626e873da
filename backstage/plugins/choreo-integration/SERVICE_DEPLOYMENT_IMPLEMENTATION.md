# Service Deployment Feature Implementation

## Overview

This document describes the implementation of the new service deployment feature in the Choreo console using the Backstage plugin. The feature allows users to create and deploy services (microservices/APIs) using buildpack-based deployment, complementing the existing web application creation functionality.

## Implementation Summary

### 1. Service-Specific Types and Interfaces

**Files Created/Modified:**
- `src/api/choreoApi.ts` - Added `ServiceCreationData`, `ServiceBuildConfig`, `ServiceBuildPreset` interfaces
- `backstage/plugins/choreo-integration-backend/src/services/ChoreoApiService/types.ts` - Added backend service types

**Key Interfaces:**
```typescript
interface ServiceCreationData {
  repositoryUrl: string;
  branch: string;
  componentDirectory: string;
  displayName: string;
  componentName: string;
  description: string;
  buildPreset: string;
  buildConfig: ServiceBuildConfig;
  orgId: number;
  orgHandler: string;
  projectId: string;
}

interface ServiceBuildConfig {
  languageVersion?: string;
  port?: number;
  runCommand?: string;
  dockerfilePath?: string;
  envValues?: Array<{ key: string; value: string }>;
}
```

### 2. Service Build Presets

**Files Created:**
- `src/components/ChoreoCreateServicePage/serviceBuildPresets.ts`
- `src/components/ChoreoCreateServicePage/serviceBuildPresets.test.ts`

**Supported Technologies:**
- **Node.js** (primary focus) - Language version 20.x.x, port 3000
- **Python** - Language version 3.11, port 8000
- **Java** - Language version 17, port 8080
- **Go** - Language version 1.21, port 8080
- **.NET** - Language version 8.0, port 5000
- **Ballerina** - Language version 2201.8.0, port 9090
- **PHP** - Language version 8.2, port 8080
- **Ruby** - Language version 3.2, port 3000
- **Docker** - Custom Dockerfile support, port 8080
- **WSO2 Micro Integrator** - Port 8290
- **Prism Mock** - API mock server, port 4010

### 3. Service Creation Form Components

**Files Created:**
- `src/components/ChoreoCreateServicePage/ChoreoCreateServicePage.tsx` - Main service creation page
- `src/components/ChoreoCreateServicePage/Step3ServiceBuildConfig.tsx` - Service-specific build configuration
- `src/components/ChoreoCreateServicePage/Step4ServiceReviewDeploy.tsx` - Review and deploy step
- `src/components/ChoreoCreateServicePage/index.ts` - Component exports

**Form Flow:**
1. **Repository Configuration** (reused from web apps)
   - Public GitHub Repository URL
   - Branch selection (default: "main")
   - Component Directory path

2. **Component Details** (reused from web apps)
   - Display Name (required)
   - Component Name (auto-generated, editable)
   - Description (optional)

3. **Build Configuration** (service-specific)
   - Build preset selection with visual cards
   - Language version configuration
   - Port configuration
   - Run command (for applicable presets)
   - Dockerfile path (for Docker preset)

4. **Review & Deploy**
   - Complete configuration review
   - Deployment context display

### 4. API Layer Updates

**Files Modified:**
- `src/api/ChoreoClient.ts` - Added `createServiceComponent` method
- `src/api/choreoApi.ts` - Added service creation interface to `ChoreoApi`

**New API Method:**
```typescript
async createServiceComponent(data: ServiceCreationData): Promise<ComponentCreationResponse>
```

### 5. Backend Implementation

**Files Modified:**
- `backstage/plugins/choreo-integration-backend/src/router.ts` - Added `/choreo-create-service` route
- `backstage/plugins/choreo-integration-backend/src/services/ChoreoApiService/createChoreoApiService.ts` - Added `createServiceComponent` method

**GraphQL Integration:**
- Uses `CreateBuildpackComponent` mutation instead of `CreateByocComponent`
- Maps service presets to appropriate buildpack configurations
- Supports buildpack-specific configuration (languageVersion, buildpackId, runCommand, etc.)

**GraphQL Mutation Structure:**
```graphql
mutation CreateBuildpackComponent($component: ByocCreateComponentSchema!) {
  createBuildpackComponent(component: $component) {
    id
    createdAt
    updatedAt
    name
    handle
    organizationId
    projectId
    orgHandle
    type
    description
    imageRegistryId
    imageRegistry {
      id
      createdAt
      updatedAt
      cloudConnectorId
      imageRepositoryName
    }
    componentType
    httpBased
  }
}
```

### 6. Routes and Plugin Configuration

**Files Modified:**
- `src/routes.ts` - Added `createServiceRouteRef`
- `src/plugin.ts` - Added service creation route and page export
- `src/index.ts` - Exported `ChoreoCreateServicePage`
- `src/components/ChoreoCreateComponentPage/ChoreoCreateComponentPage.tsx` - Updated to use service route

**New Route:**
- `/organizations/{orgHandler}/projects/{projectId}/create/service`

## Key Differences from Web App Creation

1. **GraphQL Mutation**: Uses `CreateBuildpackComponent` instead of `CreateByocComponent`
2. **Component Type**: Uses `buildpack` instead of `byocWebAppsDockerfileLess`
3. **Configuration**: Focuses on runtime configuration (language version, port, run command) rather than build configuration (build command, output directory)
4. **Buildpack Mapping**: Each preset maps to a specific buildpack ID for the Choreo platform

## Testing

- Comprehensive test suite for service build presets
- All tests passing (11/11)
- Validates preset structure, configuration, and helper functions

## Usage

1. Navigate to the Choreo components page
2. Click "Create Component"
3. Select "Service" option
4. Follow the 4-step wizard:
   - Configure repository details
   - Set component information
   - Choose build preset and configure settings
   - Review and deploy

## Node.js Focus

The implementation prioritizes Node.js as requested:
- Node.js is the default preset
- Comprehensive configuration options (language version 20.x.x, port 3000, npm start command)
- Proper buildpack mapping for Node.js applications
- Validation and testing specifically for Node.js preset

## Future Enhancements

- Dynamic buildpack ID resolution from Choreo API
- Environment variable configuration UI
- Advanced buildpack configuration options
- Integration with Choreo deployment tracking
- Support for private repositories
