{"name": "@internal/plugin-catalog-backend-module-choreo", "version": "0.1.0", "license": "Apache-2.0", "private": true, "description": "The choreo backend module for the catalog plugin.", "main": "src/index.ts", "types": "src/index.ts", "publishConfig": {"access": "public", "main": "dist/index.cjs.js", "types": "dist/index.d.ts"}, "backstage": {"role": "backend-plugin-module"}, "scripts": {"start": "backstage-cli package start", "build": "backstage-cli package build", "lint": "backstage-cli package lint", "test": "backstage-cli package test", "clean": "backstage-cli package clean", "prepack": "backstage-cli package prepack", "postpack": "backstage-cli package postpack"}, "dependencies": {"@backstage/backend-plugin-api": "^1.4.1", "@backstage/catalog-client": "^1.9.1", "@backstage/catalog-model": "^1.7.3", "@backstage/config": "^1.2.0", "@backstage/plugin-catalog-node": "^1.17.2", "@backstage/plugin-permission-common": "^0.8.4", "@internal/plugin-choreo-integration-backend": "*"}, "devDependencies": {"@backstage/backend-test-utils": "^1.7.0", "@backstage/cli": "^0.33.1", "@types/jest": "^29.5.5", "jest": "^29.7.0"}, "files": ["dist", "config.d.ts"], "configSchema": "config.d.ts"}