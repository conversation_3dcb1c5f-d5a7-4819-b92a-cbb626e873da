# catalog-backend-module-choreo

This is a Backstage catalog backend module that integrates Choreo components into the Backstage Software Catalog as discoverable entities.

## Overview

The Choreo catalog backend module automatically discovers and imports Choreo organizations, projects, and components as Backstage catalog entities:

- **Choreo Organizations** → **Backstage Domain entities**
- **Choreo Projects** → **Backstage System entities** (linked to domains)
- **Choreo Components** → **Backstage Component entities** (linked to systems)

This enables users to discover Choreo components through Backstage's native search and filtering capabilities.

## Installation

Install the module to your backend package:

```bash
# From your root directory
yarn --cwd packages/backend add @internal/plugin-catalog-backend-module-choreo
```

Then add the module to your backend in `packages/backend/src/index.ts`:

```ts
const backend = createBackend();
// ... other plugins
backend.add(import('@internal/plugin-catalog-backend-module-choreo'));
```

## Configuration

The module extends the existing `choreo` configuration section. Add the following to your `app-config.yaml`:

```yaml
choreo:
  # Required: Existing Choreo API configuration
  apiUrl: ${CHOREO_API_URL}
  projectApiUrl: ${CHOREO_PROJECT_API_URL}
  bearerToken: ${CHOREO_BEARER_TOKEN}
  
  # Optional: Catalog-specific configuration
  catalog:
    schedule:
      frequency: 300  # Sync frequency in seconds (default: 300 = 5 minutes)
      timeout: 120    # Sync timeout in seconds (default: 120 = 2 minutes)
```

## Entity Mapping

### Domain Entities (Organizations)
- **Name**: Organization handle
- **Title**: Organization name
- **Annotations**: `choreo.io/organization-*` metadata
- **Owner**: `guests` (configurable)

### System Entities (Projects)
- **Name**: `{orgHandle}-{projectHandler}`
- **Title**: Project name
- **Domain**: Organization handle
- **Annotations**: `choreo.io/project-*` metadata
- **Owner**: `guests` (configurable)

### Component Entities (Components)
- **Name**: `{orgHandle}-{projectName}-{componentHandler}`
- **Title**: Component display name
- **System**: `{orgHandle}-{projectName}`
- **Type**: Mapped from Choreo component type (Service → service, Web Application → website, etc.)
- **Lifecycle**: Mapped from Choreo status (Active → production, Development → experimental, etc.)
- **Annotations**: `choreo.io/component-*` metadata
- **Owner**: `guests` (configurable)

## Features

- **Automatic Discovery**: Periodically syncs Choreo entities with configurable frequency
- **Hierarchical Relationships**: Maintains proper domain → system → component relationships
- **Rich Metadata**: Preserves Choreo-specific metadata as annotations
- **Error Handling**: Graceful handling of API failures with detailed logging
- **Type Safety**: Full TypeScript support with proper type definitions

## Dependencies

This module depends on:
- `@internal/plugin-choreo-integration-backend` - For Choreo API service
- Standard Backstage catalog and backend dependencies

## Development

```bash
# Build the module
yarn build

# Run tests
yarn test

# Lint code
yarn lint
```

## Troubleshooting

### Module not loading
- Ensure the module is properly added to your backend index.ts
- Check that the `choreo` configuration section is present
- Verify that the Choreo API credentials are valid

### No entities appearing
- Check the backend logs for sync errors
- Verify that the Choreo API is accessible from your backend
- Ensure the bearer token has sufficient permissions

### Sync frequency issues
- Adjust the `choreo.catalog.schedule.frequency` setting
- Monitor backend logs for sync timing information
- Consider increasing timeout if syncs are failing due to timeouts
