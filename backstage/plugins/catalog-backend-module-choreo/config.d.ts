export interface Config {
  choreo?: {
    /**
     * Base URL for the Choreo API
     */
    apiUrl: string;
    /**
     * Project API URL for GraphQL queries
     */
    projectApiUrl: string;
    /**
     * Bearer token for authentication
     */
    bearerToken: string;
    /**
     * Optional catalog-specific configuration
     */
    catalog?: {
      /**
       * Schedule configuration for entity synchronization
       */
      schedule?: {
        /**
         * Frequency of synchronization in seconds (default: 300)
         */
        frequency?: number;
        /**
         * Timeout for synchronization in seconds (default: 120)
         */
        timeout?: number;
      };
    };
  };
}
