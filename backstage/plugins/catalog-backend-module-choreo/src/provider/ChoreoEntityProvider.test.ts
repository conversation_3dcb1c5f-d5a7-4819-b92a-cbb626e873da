import { ChoreoEntityProvider } from './ChoreoEntityProvider';
import { SchedulerServiceTaskRunner } from '@backstage/backend-plugin-api';
import { Config } from '@backstage/config';
import { LoggerService } from '@backstage/backend-plugin-api';

// Mock the ChoreoApiService
jest.mock('@internal/plugin-choreo-integration-backend/src/services/ChoreoApiService', () => ({
  createChoreoApiService: jest.fn(),
}));

describe('ChoreoEntityProvider', () => {
  let provider: ChoreoEntityProvider;
  let mockTaskRunner: jest.Mocked<SchedulerServiceTaskRunner>;
  let mockLogger: jest.Mocked<LoggerService>;
  let mockConfig: jest.Mocked<Config>;

  beforeEach(() => {
    mockTaskRunner = {
      run: jest.fn(),
    } as any;

    mockLogger = {
      info: jest.fn(),
      warn: jest.fn(),
      error: jest.fn(),
    } as any;

    mockConfig = {
      getString: jest.fn(),
      getOptionalConfig: jest.fn(),
    } as any;

    provider = new ChoreoEntityProvider(mockTaskRunner, mockLogger, mockConfig);
  });

  describe('getProviderName', () => {
    it('should return the correct provider name', () => {
      expect(provider.getProviderName()).toBe('ChoreoEntityProvider');
    });
  });

  describe('connect', () => {
    it('should initialize connection and start task runner', async () => {
      const mockConnection = {
        applyMutation: jest.fn(),
      } as any;

      await provider.connect(mockConnection);

      expect(mockTaskRunner.run).toHaveBeenCalledWith({
        id: 'ChoreoEntityProvider',
        fn: expect.any(Function),
      });
    });
  });

  describe('entity transformation', () => {
    it('should create domain entity from organization', () => {
      const mockOrg = {
        id: '123',
        uuid: 'uuid-123',
        handle: 'test-org',
        name: 'Test Organization',
        owner: {
          id: 'owner-123',
          idpId: 'idp-123',
          createdAt: '2023-01-01T00:00:00Z',
        },
      };

      // Access the private method for testing
      const domainEntity = (provider as any).translateOrganizationToDomain(mockOrg);

      expect(domainEntity).toEqual({
        apiVersion: 'backstage.io/v1alpha1',
        kind: 'Domain',
        metadata: {
          name: 'test-org',
          title: 'Test Organization',
          description: 'Choreo organization: Test Organization',
          tags: ['choreo', 'organization', 'domain'],
          annotations: {
            'backstage.io/managed-by-location': 'provider:ChoreoEntityProvider',
            'backstage.io/managed-by-origin-location': 'provider:ChoreoEntityProvider',
            'choreo.io/organization-id': '123',
            'choreo.io/organization-uuid': 'uuid-123',
            'choreo.io/organization-handle': 'test-org',
            'choreo.io/organization-name': 'Test Organization',
            'choreo.io/owner-id': 'owner-123',
            'choreo.io/owner-idp-id': 'idp-123',
            'choreo.io/created-at': '2023-01-01T00:00:00Z',
          },
          labels: {
            'choreo.io/managed': 'true',
          },
        },
        spec: {
          owner: 'guests',
        },
      });
    });

    it('should create system entity from project', () => {
      const mockProject = {
        id: 'proj-123',
        orgId: '123',
        name: 'Test Project',
        version: '1.0.0',
        createdDate: '2023-01-01T00:00:00Z',
        handler: 'test-project',
        region: 'us-east-1',
        description: 'A test project',
        updatedAt: '2023-01-02T00:00:00Z',
      };

      const systemEntity = (provider as any).translateProjectToSystem(mockProject, 'test-org');

      expect(systemEntity.kind).toBe('System');
      expect(systemEntity.metadata.name).toBe('test-org-test-project');
      expect(systemEntity.metadata.title).toBe('Test Project');
      expect(systemEntity.spec.domain).toBe('test-org');
      expect(systemEntity.metadata.annotations['choreo.io/project-id']).toBe('proj-123');
    });

    it('should create component entity from component', () => {
      const mockComponent = {
        projectId: 'proj-123',
        id: 'comp-123',
        name: 'test-component',
        handler: 'test-component',
        displayName: 'Test Component',
        displayType: 'Service',
        version: '1.0.0',
        createdAt: '2023-01-01T00:00:00Z',
        status: 'Active',
        initStatus: 'Completed',
        orgHandler: 'test-org',
        isSystemComponent: false,
        componentSubType: 'microservice',
      };

      const componentEntity = (provider as any).translateComponentToEntity(
        mockComponent,
        'test-org',
        'test-project',
      );

      expect(componentEntity.kind).toBe('Component');
      expect(componentEntity.metadata.name).toBe('test-org-test-project-test-component');
      expect(componentEntity.metadata.title).toBe('Test Component');
      expect(componentEntity.spec.system).toBe('test-org-test-project');
      expect(componentEntity.spec.type).toBe('service');
      expect(componentEntity.spec.lifecycle).toBe('production');
      expect(componentEntity.metadata.annotations['choreo.io/component-id']).toBe('comp-123');
    });
  });
});
