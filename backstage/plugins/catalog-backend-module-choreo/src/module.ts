import {
  coreServices,
  createBackendModule,
} from '@backstage/backend-plugin-api';
import { catalogProcessingExtensionPoint } from '@backstage/plugin-catalog-node/alpha';
import { ChoreoEntityProvider } from './provider/ChoreoEntityProvider';

/**
 * Choreo catalog backend module
 *
 * @public
 */
export const catalogModuleChoreo = createBackendModule({
  pluginId: 'catalog',
  moduleId: 'choreo',
  register(env) {
    env.registerInit({
      deps: {
        catalog: catalogProcessingExtensionPoint,
        config: coreServices.rootConfig,
        logger: coreServices.logger,
        scheduler: coreServices.scheduler,
      },
      async init({ catalog, config, logger, scheduler }) {
        const choreoConfig = config.getOptionalConfig('choreo');
        
        if (!choreoConfig) {
          logger.warn('Choreo configuration not found. Skipping Choreo catalog integration.');
          return;
        }

        // Get catalog-specific configuration with defaults
        const catalogConfig = choreoConfig.getOptionalConfig('catalog');
        const frequency = catalogConfig?.getOptionalNumber('schedule.frequency') ?? 300; // 5 minutes default
        const timeout = catalogConfig?.getOptionalNumber('schedule.timeout') ?? 120; // 2 minutes default
        
        const taskRunner = scheduler.createScheduledTaskRunner({
          frequency: { seconds: frequency },
          timeout: { seconds: timeout },
        });

        logger.info(`Registering Choreo entity provider with frequency: ${frequency}s, timeout: ${timeout}s`);

        catalog.addEntityProvider(
          new ChoreoEntityProvider(taskRunner, logger, config),
        );
      },
    });
  },
});
