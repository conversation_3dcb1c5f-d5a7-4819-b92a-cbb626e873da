# Backend Integration Example

This document shows how to integrate the Choreo catalog backend module into your Backstage backend.

## 1. Install the Module

```bash
# From your root directory
yarn --cwd packages/backend add @internal/plugin-catalog-backend-module-choreo
```

## 2. Add to Backend

Update your `packages/backend/src/index.ts`:

```typescript
import { createBackend } from '@backstage/backend-defaults';

const backend = createBackend();

// ... existing plugins
backend.add(import('@backstage/plugin-catalog-backend'));

// Add the Choreo catalog module
backend.add(import('@internal/plugin-catalog-backend-module-choreo'));

// ... other plugins
backend.start();
```

## 3. Configuration

Add to your `app-config.yaml`:

```yaml
choreo:
  # Required: Choreo API configuration
  apiUrl: ${CHOREO_API_URL}
  projectApiUrl: ${CHOREO_PROJECT_API_URL}
  bearerToken: ${CHOREO_BEARER_TOKEN}
  
  # Optional: Catalog-specific configuration
  catalog:
    schedule:
      frequency: 300  # Sync every 5 minutes
      timeout: 120    # 2 minute timeout
```

## 4. Environment Variables

Set the following environment variables:

```bash
export CHOREO_API_URL="https://api.choreo.dev"
export CHOREO_PROJECT_API_URL="https://console.choreo.dev/api"
export CHOREO_BEARER_TOKEN="your-bearer-token"
```

## 5. Verify Integration

After starting your backend, you should see:

1. **Logs**: Look for messages like:
   ```
   [catalog-backend-module-choreo] Registering Choreo entity provider with frequency: 300s, timeout: 120s
   [catalog-backend-module-choreo] Fetching organizations and projects from Choreo API
   [catalog-backend-module-choreo] Successfully processed X entities (Y domains, Z systems, W components)
   ```

2. **Catalog Entities**: In the Backstage UI, navigate to the Software Catalog and filter by:
   - **Domains**: Look for entities with `choreo.io/managed: true` label
   - **Systems**: Projects from your Choreo organizations
   - **Components**: Components from your Choreo projects

3. **Entity Details**: Click on any Choreo entity to see:
   - Rich metadata in annotations (prefixed with `choreo.io/`)
   - Proper relationships (components linked to systems, systems linked to domains)
   - Choreo-specific tags and labels

## 6. Troubleshooting

### No entities appearing
- Check backend logs for errors
- Verify Choreo API credentials
- Ensure network connectivity to Choreo APIs

### Sync issues
- Adjust frequency/timeout in configuration
- Monitor backend logs during sync cycles
- Check Choreo API rate limits

### Permission issues
- Verify bearer token has required permissions
- Check organization access in Choreo console
