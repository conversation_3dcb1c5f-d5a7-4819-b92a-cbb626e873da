# Choreo Platform Capabilities Analysis

Based on comprehensive analysis of the Choreo Console application codebase, this document categorizes the platform capabilities available to different user roles. Choreo is an Internal Developer Platform (IDP) that serves both developers and platform engineers with various functionalities.

## Common Platform Capabilities
*Available to all authenticated users*

### Project Management
- **Functions**: Create, read, update, delete projects; manage project metadata and settings
- **API Methods**: 
  - GraphQL: `getProjects()`, `getProject()`, `createProject()`, `updateProject()`, `deleteProject()`
  - REST: `/orgs/{orgId}/project` (GET, POST)
- **Implementation Details**: 
  - Service: `src/data/api/project.ts`
  - Hooks: `src/hooks/project.tsx`
  - Uses GraphQL queries sent as POST requests to `projectApiBaseUrl`

### Component Management
- **Functions**: Create, deploy, and manage application components; view component status and metrics
- **API Methods**:
  - GraphQL: `getComponents()`, `getComponentById()`, `updateComponent()`, `createComponent()`
  - REST: Component lifecycle endpoints
- **Implementation Details**:
  - Service: `src/data/api/component.ts`
  - Hooks: `src/hooks/component.tsx`
  - Context: `src/contexts/ComponentContextProvider.tsx`

### Organization Access
- **Functions**: View organization information, switch between organizations
- **API Methods**:
  - REST: `GET /orgs/{orgHandle}` 
  - GraphQL: `organizationGovernancePolicies` query
- **Implementation Details**:
  - Service: `src/data/api/organizations.ts`
  - Hooks: `src/hooks/organizations.tsx`
  - Uses `organizationApiUrl` for REST endpoints

### Basic Observability
- **Functions**: View application logs, metrics, and basic monitoring data
- **API Methods**:
  - GraphQL: `hubbleRequestMetrics`, component metrics queries
  - REST: Observability API endpoints
- **Implementation Details**:
  - Service: `src/data/api/observability.ts`
  - Hooks: `src/hooks/observability/`
  - Multiple observability contexts and providers

## Developer-Specific Capabilities

### Application Development
- **Functions**: Code development, testing, debugging, local development setup
- **API Methods**:
  - GraphQL: Repository and branch management queries
  - REST: Local development endpoints
- **Implementation Details**:
  - Service: `src/data/api/localDevelopment.ts`
  - Hooks: `src/hooks/localDevelopment.tsx`
  - Integration with Git repositories and build systems

### API Management
- **Functions**: Create and manage APIs, configure endpoints, handle API governance
- **API Methods**:
  - GraphQL: API creation and management mutations
  - REST: API proxy and endpoint configuration
- **Implementation Details**:
  - Service: `src/data/api/endpoint.ts`
  - Hooks: `src/hooks/apis.tsx`, `src/hooks/apiEndpoint.tsx`
  - API governance through `src/data/api/api-governance.ts`

### Testing and Quality Assurance
- **Functions**: Run tests, view test results, manage test configurations
- **API Methods**:
  - GraphQL: Test execution and result queries
  - REST: Test management endpoints
- **Implementation Details**:
  - Service: `src/data/api/testView.ts`
  - Hooks: `src/hooks/testView.tsx`
  - Context: `src/contexts/TestViewContext.tsx`

### Marketplace Integration
- **Functions**: Browse and integrate marketplace components and services
- **API Methods**:
  - GraphQL: Marketplace queries and component discovery
  - REST: Internal marketplace endpoints
- **Implementation Details**:
  - Service: `src/data/api/internalMarketplace.ts`
  - Hooks: `src/hooks/marketplace.tsx`
  - Integration with external service catalogs

## Platform Engineer-Specific Capabilities

### Infrastructure Management
- **Functions**: Manage environments, deployment pipelines, infrastructure resources
- **API Methods**:
  - REST: `/api/v1/organizations/{orgId}/environment-templates`
  - REST: `/api/v1/organizations/{orgUuid}/deployment-pipelines`
  - GraphQL: Environment and infrastructure queries
- **Implementation Details**:
  - Service: `src/data/api/devops.ts`, `src/data/api/devops/`
  - Hooks: `src/hooks/devops/`, `src/hooks/deploymentPipelines.ts`
  - Comprehensive DevOps API coverage in `devops/` subdirectory

### Security and Access Control
- **Functions**: Manage user roles, permissions, security policies, and governance
- **API Methods**:
  - REST: `/authz-mgt/v1.0/roles` (GET, POST, PUT, DELETE)
  - GraphQL: Role and permission management mutations
- **Implementation Details**:
  - Service: `src/data/api/appdev-authz.ts`
  - Hooks: `src/hooks/appdevAuthz.ts`
  - Context: `src/contexts/AccessControlContext.tsx`
  - Permission hierarchy and role-based access control

### Platform Services Management
- **Functions**: Manage databases, message brokers, and other platform services
- **API Methods**:
  - REST: Platform service provisioning and management endpoints
  - GraphQL: Service plan and configuration queries
- **Implementation Details**:
  - Services: `src/data/api/platformservices/`
  - Hooks: `src/hooks/platformservices/`
  - Database and broker server management

### Monitoring and Observability Administration
- **Functions**: Configure monitoring, alerting, and observability infrastructure
- **API Methods**:
  - GraphQL: Advanced observability and monitoring queries
  - REST: Alert configuration and monitoring setup
- **Implementation Details**:
  - Service: `src/data/api/alerting.ts`
  - Hooks: `src/hooks/alerting.tsx`
  - Advanced observability features in `src/hooks/observability/`

### Cost Management and Billing
- **Functions**: Monitor usage, manage subscriptions, cost optimization
- **API Methods**:
  - REST: Subscription and billing endpoints
  - GraphQL: Usage and cost queries
- **Implementation Details**:
  - Service: `src/data/api/billing.tsx`
  - Hooks: `src/hooks/BillingAndSubscriptions/`
  - Cost insights through `src/hooks/costInsights.tsx`

## Role-Based Access Control Patterns

### Permission System
- **Hierarchical Permissions**: Organization → Project → Component level permissions
- **Scope-Based Access**: Uses OAuth2 scopes for fine-grained access control
- **Implementation**: `src/contexts/AccessControlContext.tsx` with `hasPermission()` and `hasPermissionV2()` methods

### Authentication Integration
- **OAuth2/OIDC**: Asgardeo SDK integration for authentication
- **JWT Tokens**: Automatic token attachment to all API requests
- **Multi-Resource Support**: Different resource servers for different capabilities

### Permission Enforcement
- **API Level**: Permission checks in API service layer
- **UI Level**: Conditional rendering based on user permissions
- **Context-Aware**: Permission checks consider current organization/project context

## Technical Implementation Notes

### API Architecture
- **Hybrid Approach**: GraphQL queries sent as POST requests to REST endpoints
- **Type Safety**: Comprehensive TypeScript types for all API interactions
- **Error Handling**: Consistent error handling patterns across all services
- **Caching**: React Query for intelligent caching and state management

### Code Organization
- **Domain-Driven**: APIs organized by business domain (80+ API service files)
- **Consistent Patterns**: Standardized approach across all capability areas
- **Separation of Concerns**: Clear separation between API, hooks, and UI layers
- **Reusable Utilities**: Common HTTP and GraphQL utilities for all services
