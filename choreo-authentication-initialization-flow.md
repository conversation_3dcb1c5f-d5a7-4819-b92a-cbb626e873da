# Choreo Console Authentication and Initialization Flow

## Overview

This document provides a comprehensive trace of the authentication and initialization flow that occurs after a user successfully logs in to the Choreo Console application. The flow involves multiple phases from OAuth2/OIDC authentication through to initial page rendering.

## Flow Architecture

The authentication and initialization process follows this high-level sequence:

1. **OAuth2/OIDC Authentication** (Asgardeo SDK)
2. **User Validation and Organization Discovery**
3. **Organization-Specific Token Exchange**
4. **Permission and Access Control Setup**
5. **Initial Page Routing and Data Loading**

## Detailed Flow Analysis

### Phase 1: Authentication Setup and Provider Hierarchy

#### 1.1 Base Authentication Provider Setup
**File**: `src/providers/BaseProvider.tsx`

The authentication flow begins with the Asgardeo SDK configuration:

```typescript
<AuthProvider config={sdkConfig} fallback={<PreLoader />}>
```

**Key Configuration**:
- **Scopes**: `openid profile email groups choreo:org_admin choreo:org_user choreo:org_viewer choreo:project_admin choreo:project_user choreo:project_viewer choreo:component_admin choreo:component_user choreo:component_viewer`
- **Resource Server URLs**: Multiple API endpoints for different services
- **Grant Types**: `authorization_code`, `refresh_token`, custom organization token exchange

#### 1.2 Provider Hierarchy Initialization
**File**: `src/providers/Providers.tsx`

The application establishes a nested provider hierarchy:
```
AuthProvider → ChoreoUserProvider → AccessControlProvider → PersistQueryClientProvider
```

**React Query Configuration**:
- **Retry**: `false`
- **Refetch on Window Focus**: `false`
- **Stale Time**: `1000ms`
- **Cache Time**: `24 hours`
- **IndexedDB Persistence**: For specific query types

### Phase 2: User Context Initialization

#### 2.1 Authentication State Detection
**File**: `src/ProtectedRoute.tsx` (Lines 253-278)

When the authentication state loads, the system:

1. **Checks Authentication Status**: Uses `useAuthContext()` to determine if user is authenticated
2. **Handles Sign-In Parameters**: Extracts state parameters from URL if coming from sign-in flow
3. **Triggers Silent Sign-In**: If not authenticated, attempts silent authentication

#### 2.2 User Validation and Organization Discovery
**File**: `src/hooks/auth.tsx` (Lines 183-251)

**Critical API Call**: `useValidateUser()`
- **Endpoint**: `${usersManagementApiUrl}/validate/user`
- **Method**: POST
- **Purpose**: Validates user session and retrieves user organizations

**Response Data**:
```typescript
{
  organizations: Organization[],
  idpId: string,
  // Additional user metadata
}
```

**Alternative Flow**: If user validation is not required (page reload scenario):
- **Endpoint**: `${organizationApiUrl}/orgs`
- **Method**: GET
- **Purpose**: Fetch user organizations directly

### Phase 3: Organization-Specific Token Exchange

#### 3.1 Default Organization Selection
**File**: `src/hooks/auth.tsx` (Lines 300-326)

**Logic**: `useGetDefaultOrgHandle()`
1. **Priority 1**: Organization specified in `returnToOrg` parameter
2. **Priority 2**: Organization matching user's display name
3. **Priority 3**: First organization in the list

#### 3.2 Organization Token Exchange
**File**: `src/contexts/ChoreoUser.tsx` and `src/hooks/auth.tsx`

**Critical API Call**: `useOrgTokenExchange()`
- **Purpose**: Exchange user token for organization-specific access token
- **Grant Type**: Custom organization token exchange
- **Scope**: Organization-specific permissions

**Process**:
1. Call organization token exchange API
2. Update authentication context with new tokens
3. Set organization admin status
4. Track login success analytics

### Phase 4: Permission and Access Control Setup

#### 4.1 Access Control Context Population
**File**: `src/contexts/AccessControlContext.tsx`

The AccessControlContext is populated with:
- **Hierarchical Permissions**: Organization → Project → Component level
- **Resource-Level Mappings**: Specific resource access permissions
- **Permission Methods**: `hasPermission()`, `hasPermissionV2()`

#### 4.2 Organization Admin Status Check
**File**: `src/hooks/api/Choreo/Orgs.tsx`

**API Call**: `useGetIsOrgAdmin()`
- **Purpose**: Determine if user has organization admin privileges
- **Used For**: UI rendering and permission checks

#### 4.3 User Perspectives Loading
**File**: `src/hooks/perspectives.tsx`

**API Call**: `useGetUserPerspectives()`
- **Purpose**: Determine available user perspectives (Developer vs Platform Engineer)
- **Used For**: Initial page routing decisions

### Phase 5: Initial Page Routing and Rendering

#### 5.1 Protected Route Resolution
**File**: `src/ProtectedRoute.tsx` (Lines 214, 156)

After successful authentication and token exchange:
1. **Navigate to Protected Path**: Calls `navigateToProtectedPath(defaultOrgHandle, returnToUrl)`
2. **System Status Check**: Optional banner status API call for organization limits

#### 5.2 Initial Page Determination
**File**: `src/hooks/auth.tsx` (Lines 328-380)

**Logic**: `useNavigateToProtectedPath()`

**Decision Tree**:
1. **If returnToUrl is root (`/`)**:
   - **PE-Only User**: Redirect to PE Overview (`/pe/organizations/{orgHandle}/overview`)
   - **Last Perspective was PE**: Redirect to PE Overview
   - **Default**: Redirect to Projects redirect page (`/organizations/{orgHandle}/projects/redirect`)

2. **If returnToUrl is specific path**:
   - **Exact Org Path**: Redirect to projects redirect page
   - **Other Paths**: Navigate to specified path

#### 5.3 Organization Home Page Loading
**File**: `src/pages/Organization.routes.tsx` (Lines 104-108)

**Default Route**: `/organizations/{orgHandle}/home`
- **Component**: `OrganizationHome`
- **Fallback**: Any organization path redirects to `/home`

#### 5.4 Home Page Data Requirements
**File**: `src/pages/OrganizationHome/OrganizationHome.tsx`

**Critical Data Loading**:
1. **Projects List**: `useProjects()` hook
   - **Loading State**: Shows `PageLoaderContainer` with `LoadingLogo`
   - **Data**: User's accessible projects in the organization

2. **Subscription State**: `useSubscriptionState()`
   - **Purpose**: Determine project creation permissions

3. **Enterprise User Status**: `useEnterpriseUser()`
   - **Purpose**: UI customization based on user type

## Critical Path Analysis

### Essential API Calls (Must Complete Before User Interaction)

1. **Authentication Token Validation** (Asgardeo SDK)
2. **User Validation**: `POST ${usersManagementApiUrl}/validate/user`
3. **Organization Token Exchange** (Custom grant)
4. **Organization Admin Check**: Organization admin status API
5. **User Perspectives**: Available perspectives API
6. **Projects List**: `GET` projects for organization home page

### Optional/Parallel API Calls

1. **System Status**: `GET ${orgManagementApiUrlV2}/system-status/${orgHandle}` (banner status)
2. **User Session Extension**: Periodic session extension calls

### Performance Optimizations

1. **React Query Caching**: 24-hour cache for stable data
2. **IndexedDB Persistence**: Offline capability for cached queries
3. **Lazy Loading**: Main application components are code-split
4. **Parallel Loading**: Non-blocking API calls where possible

### Error Handling and Fallbacks

1. **Silent Sign-In Retry**: Automatic retry on token refresh failures
2. **Session Recovery**: Handles third-party cookie restrictions
3. **Error Boundaries**: Graceful degradation for API failures
4. **Timeout Handling**: Idle timeout detection and user notification

## Session Management

### Local Storage Persistence
**File**: `src/data/User.ts`

- **Key**: `'choreo-user'`
- **Data**: User session state and organization tracking
- **Methods**: `User.init()`, `User.getUser()`

### Session Extension
**File**: `src/pages/Protected.tsx` (Lines 100-112)

- **Interval**: Every 3 seconds (configurable)
- **Method**: `extendUserSession(iskFromSession)`
- **Purpose**: Maintain active session state

## Security Considerations

1. **Token Binding**: UUID-based token binding for security
2. **Scope-Based Access**: Fine-grained OAuth2 scopes
3. **Organization Isolation**: Token exchange ensures organization-specific access
4. **Session Validation**: Continuous session validation and extension

---

# Detailed A-Z Process Breakdown

## 3. Organization and Project Loading (A-Z)

### A. Organization Token Exchange Initiation
**File**: `src/hooks/auth.tsx` (Lines 69-136)

**Process**: `useOrgTokenExchange()` hook implementation
1. **Hook Setup**: Creates exchange function, progress state, and error handling
2. **Authentication Check**: Validates `orgHandle` and `isAuthenticated` status
3. **Progress State**: Sets `setIsOrgTokenProgress(true)` to show loading UI
4. **Config Update**: Calls `updateConfig({ validateIDToken: false })` to prepare for token exchange

### B. Decoded ID Token Extraction
**File**: `src/hooks/auth.tsx` (Lines 93-103)

**Process**: Extract ISK (Identity Session Key) from current token
1. **Token Retrieval**: Calls `getDecodedIDToken()` from Asgardeo SDK
2. **ISK Validation**: Checks for `decodedIDToken.isk` claim presence
3. **ISK Storage**: Calls `setAsgardeoISK(decodedIDToken.isk)` for session extension
4. **Error Handling**: Logs warning if ISK claim is missing

### C. Custom Grant Request Setup
**File**: `src/hooks/auth.tsx` (Lines 105-133)

**Process**: Prepare organization-specific token exchange
1. **Promise Creation**: Creates `promisedTokenExchange` Promise for async handling
2. **Event Registration**: Registers `Hooks.CustomGrant` event listener
3. **Grant Configuration**: Uses `tokenExchangeConfig` with organization handle
4. **Request Execution**: Calls `requestCustomGrant()` with organization data

### D. Organization Admin Status Check
**File**: `src/hooks/auth.tsx` (Lines 111-113)

**Process**: Determine user's administrative privileges
1. **API Call**: Executes `getIsOrgAdmin(orgHandle)`
2. **Status Update**: Calls `setIsOrgAdmin(isAdmin)` to update context
3. **Promise Resolution**: Resolves with admin status boolean
4. **Error Handling**: Catches and logs any admin check failures

### E. Organization Context Update
**File**: `src/contexts/ChoreoUser.tsx` (Lines 81-82)

**Process**: Update ChoreoUserContext with new organization state
1. **Hook Integration**: Uses `useOrgTokenExchange(setIsOrgAdmin)`
2. **State Management**: Returns `[exchangeOrgToken, isOrgTokenProgress]`
3. **Context Provision**: Provides exchange function to child components
4. **Progress Tracking**: Exposes loading state for UI feedback

### F. User Session Tracking
**File**: `src/contexts/ChoreoUser.tsx` (Lines 87-109)

**Process**: Track organization changes and update user session
1. **Organization Detection**: Uses `useSelectedOrgHandleFromUrl()` to get current org
2. **User Retrieval**: Calls `User.getUser()` to get current user session
3. **Organization Comparison**: Compares `selectedOrgHandle` with `currentUser.organization`
4. **Session Update**: Calls `User.init({ organization: selectedOrgHandle })` if changed

### G. Project List Context Initialization
**File**: `src/contexts/ProjectListContext.tsx` (Lines 62-100)

**Process**: Setup project data fetching and management
1. **Context Setup**: Creates `ProjectListProvider` with state management
2. **Organization Dependency**: Uses `useChoreoUser()` to get `selectedOrg`
3. **Query Configuration**: Sets up React Query with organization ID dependency
4. **API Integration**: Calls `getProjects(selectedOrg.id)` for project data

### H. Project Data Fetching
**File**: `src/contexts/ProjectListContext.tsx` (Lines 95-99)

**Process**: Execute GraphQL query for organization projects
1. **Query Key**: Uses `['projectList', selectedOrg.id]` for caching
2. **API Call**: Executes `getProjects(selectedOrg.id)` GraphQL query
3. **Data Extraction**: Returns `response?.data.data.projects || null`
4. **Error Handling**: Includes error state and refetch capabilities

### I. Project List State Management
**File**: `src/hooks/projects.ts` (Lines 27-48)

**Process**: Provide project data to components via `useProjects()` hook
1. **Context Access**: Uses `useContext(ProjectListContext)` to get project state
2. **State Exposure**: Returns `[projectList, isProjectListLoading, refetchProjectList, projectListError, isProjectListFetching]`
3. **Loading States**: Provides both loading and fetching states for UI
4. **Error Handling**: Exposes error messages for user feedback

### J. Component Type Counting
**File**: `src/hooks/projects.ts` (Lines 50-81)

**Process**: Fetch organization-level component statistics
1. **Organization Context**: Uses `useChoreoUser()` to get `selectedOrg.id`
2. **Query Setup**: Creates React Query with `['orgComponentCounts', { orgId }]`
3. **API Call**: Executes `getDistinctComponentTypeCounts(orgId)`
4. **Data Processing**: Returns component type counts for dashboard display

---

## 4. Permission and Access Control Setup (A-Z)

### A. Access Control Context Initialization
**File**: `src/contexts/AccessControlContext.tsx` (Lines 80-97)

**Process**: Setup hierarchical permission system
1. **Provider Setup**: Creates `AccessControlProvider` with feature config
2. **Scope Access**: Uses `useAuthContext()` to get `allowedScopes`
3. **Organization Context**: Uses `useChoreoUser()` to get `selectedOrg`
4. **Permission State**: Initializes `permissions` and `resourceLevelPermissions` maps

### B. Permission Hierarchy Establishment
**File**: `src/contexts/AccessControlContext.tsx` (Lines 91-97)

**Process**: Create multi-level permission mapping structure
1. **Map Initialization**: Creates nested Map structure for permission levels
2. **Organization Level**: Sets up `PermissionLevel.ORGANIZATION` mapping
3. **Project Level**: Sets up `PermissionLevel.PROJECT` mapping
4. **Resource Mapping**: Prepares for component-level permissions

### C. Scope-Based Permission Population
**File**: `src/contexts/AccessControlContext.tsx` (Lines 102-120)

**Process**: Convert OAuth2 scopes to permission mappings
1. **Scope Processing**: Uses `populatePermissionsMap()` utility function
2. **Permission Calculation**: Processes `allowedScopes` into boolean permissions
3. **Feature Integration**: Applies `featureConfig` for feature-specific permissions
4. **State Update**: Calls `setPermissions()` with calculated permission map

### D. Organization Information Fetching
**File**: `src/contexts/AccessControlContext.tsx` (Lines 130-150)

**Process**: Load organization-specific access control data
1. **API Call**: Executes `getOrganizationInformation(selectedOrg.id)`
2. **Permission Extraction**: Extracts organization-level permissions from response
3. **Resource Mapping**: Updates resource-level permission mappings
4. **Context Update**: Integrates organization permissions into access control

### E. Permission Checking Methods
**File**: `src/contexts/AccessControlContext.tsx` (Lines 160-190)

**Process**: Provide permission validation functions
1. **hasPermission()**: Basic permission checking for single or multiple permissions
2. **hasPermissionV2()**: Advanced resource-level permission checking
3. **Scope Validation**: Uses `hasScope()` utility for OAuth2 scope checking
4. **Hierarchy Support**: Supports Organization → Project → Component permission hierarchy

### F. Resource-Level Permission Updates
**File**: `src/contexts/AccessControlContext.tsx` (Lines 200-214)

**Process**: Dynamic permission mapping updates
1. **Update Function**: `updateResourceLevelPermissionMappings()` for runtime updates
2. **Resource Type**: Supports different `PermissionLevel` types
3. **Mapping Integration**: Merges new permission mappings with existing ones
4. **State Synchronization**: Updates context state with new permission mappings

### G. Feature Flag Integration
**File**: `src/contexts/AccessControlContext.tsx` (Lines 99-101)

**Process**: Disable features based on configuration
1. **Feature State**: Maintains `disabledFeature` array for feature flags
2. **Configuration Integration**: Uses `FeatureConfigInterface` for feature control
3. **Dynamic Disabling**: `disableFeature()` function for runtime feature control
4. **UI Integration**: Provides feature availability to components

### H. Permission Context Provision
**File**: `src/contexts/AccessControlContext.tsx` (Lines 53-68)

**Process**: Expose access control functionality to components
1. **Hook Creation**: `useAccessControl()` hook for component access
2. **Context Access**: Uses `useContext(AccessControlContext)` for state access
3. **Method Exposure**: Provides permission checking and update methods
4. **Type Safety**: Maintains `AccessControlInterface` type definitions

### I. Organization Admin Status Integration
**File**: `src/hooks/api/Choreo/Orgs.tsx`

**Process**: Integrate organization admin privileges into access control
1. **Admin Check**: `useGetIsOrgAdmin()` hook for admin status
2. **Permission Enhancement**: Admin status affects available permissions
3. **UI Customization**: Admin status determines UI element visibility
4. **Context Integration**: Admin status flows through ChoreoUserContext

### J. User Perspective Integration
**File**: `src/hooks/perspectives.tsx`

**Process**: Apply user perspective-based access control
1. **Perspective Loading**: `useGetUserPerspectives()` for available perspectives
2. **Role-Based Access**: Different permissions for Developer vs Platform Engineer
3. **Navigation Control**: Perspective affects available routes and features
4. **Context Integration**: Perspective data influences access control decisions

---

## 5. Initial Page Rendering (A-Z)

### A. Protected Route Resolution
**File**: `src/ProtectedRoute.tsx` (Lines 214, 156)

**Process**: Determine initial page destination after authentication
1. **Navigation Trigger**: Calls `navigateToProtectedPath(defaultOrgHandle, returnToUrl)`
2. **URL Analysis**: Analyzes `returnToUrl` parameter for destination
3. **Organization Context**: Uses `defaultOrgHandle` for organization-specific routing
4. **System Status**: Optional system status check for organization limits

### B. Navigation Path Determination
**File**: `src/hooks/auth.tsx` (Lines 328-380)

**Process**: `useNavigateToProtectedPath()` logic for initial page routing
1. **URL Parsing**: Analyzes `returnToUrl` for specific path requirements
2. **Perspective Check**: Uses user perspectives to determine appropriate landing page
3. **Default Routing**: Applies fallback routing logic for root URL access
4. **Organization Integration**: Ensures organization context in all routes

### C. Route Decision Tree Processing
**File**: `src/hooks/auth.tsx` (Lines 340-370)

**Process**: Complex routing logic based on user context
1. **Root URL Check**: If `returnToUrl` is `/`, apply perspective-based routing
2. **PE-Only User**: Redirect to Platform Engineer overview page
3. **Last Perspective**: Use stored perspective preference for routing
4. **Default Developer**: Redirect to projects redirect page for developers

### D. Organization Route Handling
**File**: `src/pages/Organization.routes.tsx` (Lines 104-108)

**Process**: Organization-level routing configuration
1. **Default Route**: Any organization path redirects to `/home` endpoint
2. **Route Matching**: Uses exact path matching for organization routes
3. **Query Preservation**: Maintains query parameters during redirects
4. **Fallback Logic**: Ensures all organization paths have valid destinations

### E. Organization Home Component Loading
**File**: `src/pages/OrganizationHome/OrganizationHome.tsx`

**Process**: Load and render organization home page
1. **Component Setup**: Initialize `OrganizationHome` component
2. **Provider Wrapping**: Uses `OrganizationHomeProvider` for context
3. **Data Dependencies**: Requires projects list and subscription state
4. **Loading State**: Shows `PageLoaderContainer` with `LoadingLogo` during data fetch

### F. Project Data Loading for Home Page
**File**: `src/pages/OrganizationHome/OrganizationHome.tsx`

**Process**: Load essential data for home page rendering
1. **Projects Hook**: Uses `useProjects()` hook for project list data
2. **Loading State**: Monitors `isProjectListLoading` for UI state
3. **Error Handling**: Handles project loading errors gracefully
4. **Data Dependency**: Home page rendering depends on project data availability

### G. Subscription State Loading
**File**: `src/pages/OrganizationHome/OrganizationHome.tsx`

**Process**: Load subscription and billing information
1. **Subscription Hook**: Uses `useSubscriptionState()` for billing data
2. **Project Creation**: Determines project creation permissions
3. **Tier Information**: Loads organization tier and limits
4. **UI Customization**: Subscription state affects available features

### H. Enterprise User Status Check
**File**: `src/pages/OrganizationHome/OrganizationHome.tsx`

**Process**: Determine user type for UI customization
1. **Enterprise Hook**: Uses `useEnterpriseUser()` for user classification
2. **UI Adaptation**: Enterprise users see different UI elements
3. **Feature Access**: Enterprise status affects feature availability
4. **Branding**: Enterprise users may see custom branding

### I. Component Rendering Pipeline
**File**: `src/pages/OrganizationHome/OrganizationHome.tsx`

**Process**: Render home page components based on loaded data
1. **Conditional Rendering**: Render components only when data is available
2. **Loading States**: Show appropriate loading indicators during data fetch
3. **Error Boundaries**: Handle rendering errors gracefully
4. **Progressive Loading**: Load and render components as data becomes available

### J. Final Page Display
**File**: `src/pages/OrganizationHome/OrganizationHome.tsx`

**Process**: Complete home page rendering with all data
1. **Data Integration**: Combine all loaded data for final rendering
2. **Interactive Elements**: Enable user interactions after full load
3. **Performance Optimization**: Optimize rendering for large project lists
4. **User Experience**: Ensure smooth transition from loading to interactive state

This comprehensive flow ensures secure, efficient user authentication and initialization while providing a smooth user experience with proper error handling and performance optimizations.
